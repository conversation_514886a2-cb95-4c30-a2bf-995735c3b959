"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const path_1 = __importDefault(require("path"));
const db_1 = require("./config/db");
const request_logger_1 = __importDefault(require("./middleware/request-logger"));
const brand_routes_1 = __importDefault(require("./routes/brand-routes"));
const product_routes_1 = __importDefault(require("./routes/product-routes"));
const permission_routes_1 = __importDefault(require("./routes/permission-routes"));
const role_routes_1 = __importDefault(require("./routes/role-routes"));
const user_routes_1 = __importDefault(require("./routes/user-routes"));
const i18n_routes_1 = __importDefault(require("./routes/i18n-routes"));
const auth_routes_1 = __importDefault(require("./routes/auth-routes"));
const product_review_routes_1 = __importDefault(require("./routes/product-review-routes"));
const brand_review_routes_1 = __importDefault(require("./routes/brand-review-routes"));
const font_user_routes_1 = __importDefault(require("./routes/font-user-routes"));
const font_auth_routes_1 = __importDefault(require("./routes/font-auth-routes"));
const thread_routes_1 = __importDefault(require("./routes/thread-routes"));
const message_routes_1 = __importDefault(require("./routes/message-routes"));
const search_routes_1 = __importDefault(require("./routes/search-routes"));
const user_stats_routes_1 = __importDefault(require("./routes/user-stats-routes"));
const history_routes_1 = __importDefault(require("./routes/history-routes"));
const dashboard_stats_routes_1 = __importDefault(require("./routes/dashboard-stats-routes"));
// In CommonJS, __dirname is available globally
const app = (0, express_1.default)();
const port = process.env.PORT || 3000;
app.use((0, cors_1.default)({
    origin: "*",
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
}));
// 添加请求日志中间件（在其他中间件之前）
app.use(request_logger_1.default.middleware());
// 允许解析JSON请求体
app.use(express_1.default.json());
// Serve static files for uploaded images
app.use('/uploads', express_1.default.static(path_1.default.join(__dirname, '../uploads')));
// Serve static files from assets folder for product images - 使用相对路径
app.use('/assets', express_1.default.static(path_1.default.join(__dirname, '../assets')));
(0, db_1.connectDB)()
    .then(() => console.log("MongoDB 数据库启动成功"))
    .catch((err) => {
    console.error("MongoDB 数据连接失败:", err);
    process.exit(1);
});
// 主页路由
app.get("/", async (req, res) => {
    res.json({ message: "好宠粮 后端 API 工作正常" });
});
// API路由
app.use("/api/brands", brand_routes_1.default);
app.use("/api/products", product_routes_1.default);
app.use("/api/permissions", permission_routes_1.default);
app.use("/api/roles", role_routes_1.default);
app.use("/api/users", user_routes_1.default);
app.use("/api/i18n", i18n_routes_1.default);
app.use("/api/auth", auth_routes_1.default);
app.use("/api/reviews", product_review_routes_1.default);
app.use('/api/brand-reviews', brand_review_routes_1.default);
app.use('/api/font-user', font_user_routes_1.default);
app.use('/api/font-auth', font_auth_routes_1.default);
app.use('/api/threads', thread_routes_1.default);
app.use('/api/messages', message_routes_1.default);
app.use('/api/search', search_routes_1.default);
app.use('/api/user-stats', user_stats_routes_1.default);
app.use('/api/history', history_routes_1.default);
app.use('/api/dashboard-stats', dashboard_stats_routes_1.default);
// 添加日志统计端点 - 分别定义两个路由
app.get("/api/logs/stats", async (req, res) => {
    try {
        const stats = await request_logger_1.default.generateStats();
        res.json({ success: true, data: stats });
    }
    catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});
app.get("/api/logs/stats/:date", async (req, res) => {
    try {
        const { date } = req.params;
        const stats = await request_logger_1.default.generateStats(date);
        res.json({ success: true, data: stats });
    }
    catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});
app.listen(port, () => {
    console.log(`服务器开启： http://localhost:${port}`);
    console.log(`请求日志将保存到: logs/ 目录`);
    console.log(`查看今日统计: GET /api/logs/stats`);
    console.log(`查看指定日期统计: GET /api/logs/stats/YYYY-MM-DD`);
});
//# sourceMappingURL=main.js.map