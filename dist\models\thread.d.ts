import mongoose, { Document } from 'mongoose';
export interface IThread extends Document {
    thread_id: string;
    user_id: string;
    title?: string;
    content: string;
    images?: string[];
    parent_thread_id?: string;
    root_thread_id?: string;
    reply_to_user_id?: string;
    reply_level: number;
    liked_by: string[];
    bookmarked_by: string[];
    like_count: number;
    reply_count: number;
    bookmark_count: number;
    status: 'visible' | 'hidden' | 'deleted';
    is_approved: boolean;
    approved_by?: string | null;
    approved_at?: Date | null;
    reject_reason?: string | null;
    created_at: Date;
    updated_at: Date;
}
declare const _default: mongoose.Model<IThread, {}, {}, {}, mongoose.Document<unknown, {}, IThread, {}> & IThread & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=thread.d.ts.map