"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const ThreadSchema = new mongoose_1.Schema({
    thread_id: { type: String, required: true, unique: true, index: true },
    user_id: { type: String, required: true, ref: 'FontUser', index: true },
    title: {
        type: String,
        required: function () {
            // 只有主帖（reply_level为0或没有parent_thread_id）才要求标题
            return this.reply_level === 0 || !this.parent_thread_id;
        },
        maxlength: 100,
        trim: true,
        validate: {
            validator: function (v) {
                // 如果是主帖，标题不能为空
                if ((this.reply_level === 0 || !this.parent_thread_id) && (!v || v.trim().length === 0)) {
                    return false;
                }
                return true;
            },
            message: 'Title is required for main posts'
        }
    },
    content: {
        type: String,
        required: true,
        maxlength: 1000,
        validate: {
            validator: function (v) {
                return v.trim().length > 0;
            },
            message: 'Content cannot be empty'
        }
    },
    images: {
        type: [{ type: String }],
        validate: {
            validator: function (v) {
                return !v || v.length <= 9;
            },
            message: 'Maximum 9 images allowed'
        }
    },
    parent_thread_id: { type: String, index: true, default: null },
    root_thread_id: { type: String, index: true, default: null },
    reply_to_user_id: { type: String, default: null },
    reply_level: {
        type: Number,
        default: 0,
        min: 0,
        max: 2,
        validate: {
            validator: function (v) {
                return v >= 0 && v <= 2;
            },
            message: 'Reply level must be 0, 1, or 2'
        }
    },
    liked_by: [{ type: String }],
    bookmarked_by: [{ type: String }],
    like_count: { type: Number, default: 0, min: 0 },
    reply_count: { type: Number, default: 0, min: 0 },
    bookmark_count: { type: Number, default: 0, min: 0 },
    status: {
        type: String,
        enum: ['visible', 'hidden', 'deleted'],
        default: 'visible'
    },
    is_approved: { type: Boolean, default: false, index: true },
    approved_by: { type: String, default: null },
    approved_at: { type: Date, default: null },
    reject_reason: { type: String, default: null },
}, {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
    versionKey: false
});
// 虚拟字段
ThreadSchema.virtual('likes').get(function () {
    return Array.isArray(this.liked_by) ? this.liked_by.length : 0;
});
ThreadSchema.virtual('bookmarks').get(function () {
    return Array.isArray(this.bookmarked_by) ? this.bookmarked_by.length : 0;
});
// 包含虚拟字段到JSON
ThreadSchema.set('toJSON', { virtuals: true });
ThreadSchema.set('toObject', { virtuals: true });
// 索引
ThreadSchema.index({ user_id: 1, created_at: -1 });
ThreadSchema.index({ parent_thread_id: 1, created_at: 1 });
ThreadSchema.index({ root_thread_id: 1, created_at: 1 });
ThreadSchema.index({ reply_level: 1, created_at: 1 });
ThreadSchema.index({ status: 1, created_at: -1 });
ThreadSchema.index({ status: 1, updated_at: -1 });
ThreadSchema.index({ like_count: -1, created_at: -1 });
ThreadSchema.index({ like_count: -1, reply_count: -1, bookmark_count: -1, created_at: -1 }); // 热门排序复合索引
ThreadSchema.index({ is_approved: 1, created_at: -1 });
// 添加文本搜索索引
ThreadSchema.index({
    title: 'text',
    content: 'text'
}, {
    weights: {
        title: 10,
        content: 5
    },
    name: 'thread_text_index'
});
exports.default = mongoose_1.default.model('Thread', ThreadSchema, 'threads');
//# sourceMappingURL=thread.js.map