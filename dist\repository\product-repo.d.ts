import { IBaseRepo } from "./base-repo";
export interface ProductData {
    _id: string;
    name: string;
    brand: string;
    product_type?: string;
    image_url?: string;
    product_url?: string;
    allergen_ingredients?: string[];
    est_calories?: string;
    quality_ingredients?: string[];
    questionable_ingredients?: string[];
    guaranteed_analysis?: any;
    dry_matter_analysis?: any;
}
export interface IProductRepo extends IBaseRepo<ProductData> {
    findAll(): Promise<ProductData[]>;
    getAll(): any;
    findById(id: string): Promise<ProductData | null>;
    findByBrand(brand: string): Promise<ProductData[]>;
    findByType(productType: string): Promise<ProductData[]>;
    searchByName(searchText: string): Promise<ProductData[]>;
    findByIngredient(ingredient: string, field: string): Promise<ProductData[]>;
    findByNutritionRange(field: string, min: number, max: number): Promise<ProductData[]>;
    findByCalorieRange(min: number, max: number): Promise<ProductData[]>;
    findByNameFuzzy(namePattern: string, skip?: number, limit?: number): Promise<ProductData[]>;
    countByNameFuzzy(namePattern: string): Promise<number>;
    getRankedProducts(name?: string, sortBy?: string, sortOrder?: string, page?: number, limit?: number): Promise<ProductData[]>;
    countRankedProducts(name?: string, sortBy?: string): Promise<number>;
    findRaw(query: any, skip?: number, limit?: number): Promise<ProductData[]>;
    countRaw(query: any): Promise<number>;
    aggregate(pipeline: any[]): Promise<any[]>;
}
declare const productRepo: IProductRepo;
export default productRepo;
//# sourceMappingURL=product-repo.d.ts.map