"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const product_1 = __importDefault(require("../models/product"));
const base_repo_1 = require("./base-repo");
class ProductRepo extends base_repo_1.BaseRepo {
    constructor() {
        super(product_1.default, '_id');
    }
    // Get all products
    async findAll() {
        const products = await product_1.default.find({});
        return products.map(p => p.toObject ? p.toObject() : p);
    }
    // Get all products with pagination support
    getAll() {
        return product_1.default.find({}); // Return query without awaiting
    }
    // Get product by ID
    async findById(id) {
        const product = await product_1.default.findById(id);
        if (!product)
            return null;
        const productObj = product.toObject ? product.toObject() : product;
        if (!productObj.guaranteed_analysis) {
            productObj.guaranteed_analysis = new Map();
        }
        if (!productObj.dry_matter_analysis) {
            productObj.dry_matter_analysis = new Map();
        }
        return productObj;
    }
    // Get products by brand
    async findByBrand(brand) {
        const products = await product_1.default.find({ brand });
        return products.map(p => p.toObject ? p.toObject() : p);
    }
    // Get products by product type
    async findByType(productType) {
        const products = await product_1.default.find({ product_type: productType });
        return products.map(p => p.toObject ? p.toObject() : p);
    }
    // Search products by name (partial match)
    async searchByName(searchText) {
        const products = await product_1.default.find({
            name: { $regex: searchText, $options: "i" },
        });
        return products.map(p => p.toObject ? p.toObject() : p);
    }
    // Find products by ingredient
    async findByIngredient(ingredient, field) {
        const products = await product_1.default.find({
            [field]: { $regex: ingredient, $options: "i" },
        });
        return products.map(p => p.toObject ? p.toObject() : p);
    }
    // Find products by nutrition range (protein, fat, etc.)
    async findByNutritionRange(field, min, max) {
        const products = await product_1.default.find({});
        return products.filter((product) => {
            const fieldPath = field.split(".");
            let value = product;
            // Navigate through object properties
            for (const path of fieldPath) {
                if (!value || !value[path])
                    return false;
                value = value[path];
            }
            // Extract number from string like "30%" or "30"
            const numValue = parseFloat(String(value));
            return !isNaN(numValue) && numValue >= min && numValue <= max;
        }).map(p => p.toObject ? p.toObject() : p);
    }
    // Find products by calorie range
    async findByCalorieRange(min, max) {
        const products = await product_1.default.find({});
        return products.filter((product) => {
            if (!product.est_calories)
                return false;
            // Extract calories from string like "348 cal/100g"
            const calorieMatch = product.est_calories.match(/(\d+)/);
            if (!calorieMatch)
                return false;
            const calories = parseInt(calorieMatch[1]);
            return calories >= min && calories <= max;
        }).map(p => p.toObject ? p.toObject() : p);
    }
    // Find products by name with fuzzy matching and pagination
    async findByNameFuzzy(namePattern, skip = 0, limit = 10) {
        const products = await product_1.default.find({
            name: { $regex: namePattern, $options: "i" },
        })
            .skip(skip)
            .limit(limit)
            .lean();
        return products;
    }
    // Count products by fuzzy name match
    async countByNameFuzzy(namePattern) {
        return await product_1.default.countDocuments({
            name: { $regex: namePattern, $options: "i" },
        });
    }
    async getRankedProducts(name, sortBy = 'quality', sortOrder = 'desc', page = 1, limit = 100) {
        const skip = (page - 1) * limit;
        let sortCriteria = {};
        const nameFilter = name ? { name: { $regex: name, $options: "i" } } : {};
        switch (sortBy) {
            case 'protein':
                sortCriteria = { 'dry_matter_analysis.protein': sortOrder === 'desc' ? -1 : 1 };
                break;
            case 'fat':
                sortCriteria = { 'dry_matter_analysis.fat': sortOrder === 'desc' ? -1 : 1 };
                break;
            case 'carbs':
                sortCriteria = { 'dry_matter_analysis.carbs': sortOrder === 'desc' ? -1 : 1 };
                break;
            case 'calories':
                sortCriteria = { 'est_calories': sortOrder === 'desc' ? -1 : 1 };
                break;
            case 'quality_ingredients':
                const qualitySortOrder = sortOrder === 'desc' ? -1 : 1;
                const products = await product_1.default.aggregate([
                    ...(name ? [{ $match: nameFilter }] : []),
                    {
                        $addFields: {
                            qualityIngredientsCount: { $size: { $ifNull: ["$quality_ingredients", []] } }
                        }
                    },
                    { $sort: { qualityIngredientsCount: qualitySortOrder } },
                    { $skip: skip },
                    { $limit: limit }
                ]);
                return products;
            case 'questionable_ingredients':
                const questionableSortOrder = sortOrder === 'desc' ? 1 : -1;
                const questionableProducts = await product_1.default.aggregate([
                    ...(name ? [{ $match: nameFilter }] : []),
                    {
                        $addFields: {
                            questionableIngredientsCount: { $size: { $ifNull: ["$questionable_ingredients", []] } }
                        }
                    },
                    { $sort: { questionableIngredientsCount: questionableSortOrder } },
                    { $skip: skip },
                    { $limit: limit }
                ]);
                return questionableProducts;
            default:
                // Default quality ranking based on protein - carbs + quality ingredients - questionable ingredients
                const defaultProducts = await product_1.default.aggregate([
                    ...(name ? [{ $match: nameFilter }] : []),
                    {
                        $addFields: {
                            qualityIngredientsCount: { $size: { $ifNull: ["$quality_ingredients", []] } },
                            questionableIngredientsCount: { $size: { $ifNull: ["$questionable_ingredients", []] } },
                            proteinValue: {
                                $toDouble: {
                                    $replaceAll: {
                                        input: { $ifNull: ["$dry_matter_analysis.protein", "0%"] },
                                        find: "%",
                                        replacement: ""
                                    }
                                }
                            },
                            carbsValue: {
                                $toDouble: {
                                    $replaceAll: {
                                        input: { $ifNull: ["$dry_matter_analysis.carbs", "0%"] },
                                        find: "%",
                                        replacement: ""
                                    }
                                }
                            },
                            qualityScore: {
                                $subtract: [
                                    { $add: [
                                            { $toDouble: {
                                                    $replaceAll: {
                                                        input: { $ifNull: ["$dry_matter_analysis.protein", "0%"] },
                                                        find: "%",
                                                        replacement: ""
                                                    }
                                                } },
                                            { $size: { $ifNull: ["$quality_ingredients", []] } }
                                        ] },
                                    { $add: [
                                            { $toDouble: {
                                                    $replaceAll: {
                                                        input: { $ifNull: ["$dry_matter_analysis.carbs", "0%"] },
                                                        find: "%",
                                                        replacement: ""
                                                    }
                                                } },
                                            { $size: { $ifNull: ["$questionable_ingredients", []] } }
                                        ] }
                                ]
                            }
                        }
                    },
                    { $sort: { qualityScore: sortOrder === 'desc' ? -1 : 1 } },
                    { $skip: skip },
                    { $limit: limit }
                ]);
                return defaultProducts;
        }
        return await product_1.default.find(nameFilter)
            .sort(sortCriteria)
            .skip(skip)
            .limit(limit)
            .lean();
    }
    async countRankedProducts(name) {
        const filter = name ? { name: { $regex: name, $options: "i" } } : {};
        return await product_1.default.countDocuments(filter);
    }
    // Raw query method for flexible searching
    async findRaw(query, skip = 0, limit = 0) {
        try {
            let queryBuilder = product_1.default.find(query);
            if (skip > 0) {
                queryBuilder = queryBuilder.skip(skip);
            }
            if (limit > 0) {
                queryBuilder = queryBuilder.limit(limit);
            }
            const products = await queryBuilder.lean();
            return products.map(product => ({
                ...product,
                _id: product._id.toString()
            }));
        }
        catch (error) {
            console.error('Error executing raw product query:', error);
            throw error;
        }
    }
    // Count documents for raw query
    async countRaw(query) {
        try {
            return await product_1.default.countDocuments(query);
        }
        catch (error) {
            console.error('Error counting products with raw query:', error);
            throw error;
        }
    }
    // 聚合查询方法
    async aggregate(pipeline) {
        try {
            return await product_1.default.aggregate(pipeline);
        }
        catch (error) {
            console.error('Error executing aggregation pipeline:', error);
            throw error;
        }
    }
}
const productRepo = new ProductRepo();
exports.default = productRepo;
//# sourceMappingURL=product-repo.js.map