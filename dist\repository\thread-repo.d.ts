import { ThreadData, ThreadWithUser } from '../types/thread-types';
import { IBaseRepo } from './base-repo';
export interface IThreadRepo extends IBaseRepo<ThreadData> {
    findPublicThreads(skip?: number, limit?: number, sortBy?: 'latest' | 'updated' | 'hot'): Promise<ThreadWithUser[]>;
    findThreadsByUserId(userId: string, skip?: number, limit?: number): Promise<ThreadWithUser[]>;
    findThreadWithComments(threadId: string, commentsPage?: number, commentsLimit?: number): Promise<{
        thread: ThreadWithUser | null;
        comments: {
            items: ThreadWithUser[];
            total: number;
            page: number;
            limit: number;
        };
    }>;
    findReplies(parentThreadId: string, skip?: number, limit?: number): Promise<ThreadWithUser[]>;
    findNestedReplies(parentThreadId: string, maxDepth?: number, currentDepth?: number): Promise<ThreadWithUser[]>;
    findTopLevelRepliesWithNested(parentThreadId: string, skip?: number, limit?: number, maxDepth?: number): Promise<ThreadWithUser[]>;
    addLike(threadId: string, userId: string): Promise<ThreadData | null>;
    removeLike(threadId: string, userId: string): Promise<ThreadData | null>;
    addBookmark(threadId: string, userId: string): Promise<ThreadData | null>;
    removeBookmark(threadId: string, userId: string): Promise<ThreadData | null>;
    incrementReplyCount(threadId: string): Promise<ThreadData | null>;
    decrementReplyCount(threadId: string): Promise<ThreadData | null>;
    updateByThreadId(threadId: string, updateData: any): Promise<ThreadData | null>;
    updateStatus(threadId: string, status: 'visible' | 'hidden' | 'deleted'): Promise<ThreadData | null>;
    countPublicThreads(): Promise<number>;
    countThreadsByUserId(userId: string): Promise<number>;
    countReplies(parentThreadId: string): Promise<number>;
    findUserLikedThreads(userId: string, skip?: number, limit?: number): Promise<ThreadWithUser[]>;
    findUserBookmarkedThreads(userId: string, skip?: number, limit?: number): Promise<ThreadWithUser[]>;
    findUserComments(userId: string, skip?: number, limit?: number): Promise<ThreadWithUser[]>;
    countUserLikedThreads(userId: string): Promise<number>;
    countUserBookmarkedThreads(userId: string): Promise<number>;
    countUserComments(userId: string): Promise<number>;
    findByIdWithUser(threadId: string): Promise<ThreadWithUser | null>;
    getUserLikesReceivedCount(userId: string): Promise<number>;
    getUserBookmarksReceivedCount(userId: string): Promise<number>;
    findFollowingThreads(userId: string, skip?: number, limit?: number): Promise<ThreadWithUser[]>;
    countFollowingThreads(userId: string): Promise<number>;
    findByThreadId(threadId: string): Promise<ThreadData | null>;
    findRaw(query: any, skip?: number, limit?: number): Promise<ThreadData[]>;
    countRaw(query: any): Promise<number>;
    getTotalLikes(): Promise<number>;
    getTotalBookmarks(): Promise<number>;
    getTopThreads(limit: number): Promise<ThreadWithUser[]>;
    getTrendData(startDate: Date, endDate: Date): Promise<Array<{
        date: string;
        count: number;
    }>>;
    countByDateRange(startDate: Date, endDate: Date): Promise<number>;
}
declare const threadRepo: IThreadRepo;
export default threadRepo;
//# sourceMappingURL=thread-repo.d.ts.map