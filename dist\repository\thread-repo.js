"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const base_repo_1 = require("./base-repo");
const thread_1 = __importDefault(require("../models/thread"));
const font_user_1 = __importDefault(require("../models/font-user"));
class ThreadRepo extends base_repo_1.BaseRepo {
    constructor() {
        super(thread_1.default, 'thread_id');
    }
    // 获取用户信息的辅助方法
    async populateUserInfo(threads) {
        const userIds = [...new Set(threads.map(thread => thread.user_id))];
        const users = await font_user_1.default.find({ id: { $in: userIds } });
        const userMap = new Map(users.map(user => [user.id, {
                id: user.id,
                username: user.username,
                avatar: user.avatar || undefined,
                bio: user.bio || undefined
            }]));
        return threads.map(thread => ({
            ...thread,
            user: userMap.get(thread.user_id) || {
                id: thread.user_id,
                username: 'Unknown User',
                avatar: undefined,
                bio: undefined
            }
        }));
    }
    // 获取公共帖子列表（不包含评论）
    async findPublicThreads(skip = 0, limit = 10, sortBy = 'latest') {
        let sortOption = {};
        switch (sortBy) {
            case 'hot':
                // 热门排序：综合考虑点赞数、评论数、收藏数和时间
                sortOption = {
                    like_count: -1,
                    reply_count: -1,
                    bookmark_count: -1,
                    created_at: -1
                };
                break;
            case 'updated':
                // 按更新时间排序
                sortOption = { updated_at: -1 };
                break;
            case 'latest':
            default:
                // 按创建时间排序
                sortOption = { created_at: -1 };
                break;
        }
        const threads = await thread_1.default.find({
            parent_thread_id: null, // 只获取主帖，不包含评论
            status: 'visible',
            $or: [{ is_approved: true }, { is_approved: { $exists: false } }],
        })
            .sort(sortOption)
            .skip(skip)
            .limit(limit);
        const threadData = threads.map(thread => this.docToObject(thread));
        return await this.populateUserInfo(threadData);
    }
    // 获取指定用户的帖子
    async findThreadsByUserId(userId, skip = 0, limit = 10) {
        const threads = await thread_1.default.find({
            user_id: userId,
            parent_thread_id: null, // 只获取主帖
            status: { $in: ['visible', 'hidden'] } // 用户可以看到自己的隐藏帖子
        })
            .sort({ created_at: -1 })
            .skip(skip)
            .limit(limit);
        const threadData = threads.map(thread => this.docToObject(thread));
        return await this.populateUserInfo(threadData);
    }
    // 获取帖子详情和评论
    async findThreadWithComments(threadId, commentsPage = 1, commentsLimit = 10) {
        // 获取主帖
        const thread = await thread_1.default.findOne({
            thread_id: threadId,
            status: 'visible',
            $or: [{ is_approved: true }, { is_approved: { $exists: false } }],
        });
        if (!thread) {
            return {
                thread: null,
                comments: {
                    items: [],
                    total: 0,
                    page: commentsPage,
                    limit: commentsLimit
                }
            };
        }
        const threadData = this.docToObject(thread);
        const [threadWithUser] = await this.populateUserInfo([threadData]);
        // 获取带嵌套回复的顶级评论（分页）
        const skip = (commentsPage - 1) * commentsLimit;
        const comments = await this.findTopLevelRepliesWithNested(threadId, skip, commentsLimit, 3);
        const totalComments = await this.countReplies(threadId);
        return {
            thread: threadWithUser,
            comments: {
                items: comments,
                total: totalComments,
                page: commentsPage,
                limit: commentsLimit
            }
        };
    }
    // 获取回复
    async findReplies(parentThreadId, skip = 0, limit = 10) {
        const replies = await thread_1.default.find({
            parent_thread_id: parentThreadId,
            status: 'visible',
            $or: [{ is_approved: true }, { is_approved: { $exists: false } }],
        })
            .sort({ created_at: 1 }) // 评论按时间正序
            .skip(skip)
            .limit(limit);
        const repliesData = replies.map(reply => this.docToObject(reply));
        return await this.populateUserInfo(repliesData);
    }
    // 递归获取嵌套回复
    async findNestedReplies(parentThreadId, maxDepth = 3, currentDepth = 0) {
        // 防止无限递归
        if (currentDepth >= maxDepth) {
            return [];
        }
        const replies = await thread_1.default.find({
            parent_thread_id: parentThreadId,
            status: 'visible'
        }).sort({ created_at: 1 });
        if (replies.length === 0) {
            return [];
        }
        const repliesData = replies.map(reply => this.docToObject(reply));
        const repliesWithUser = await this.populateUserInfo(repliesData);
        // 为每个回复获取其嵌套回复
        const repliesWithNested = await Promise.all(repliesWithUser.map(async (reply) => {
            const nestedReplies = await this.findNestedReplies(reply.thread_id, maxDepth, currentDepth + 1);
            return {
                ...reply,
                replay_tread: nestedReplies
            };
        }));
        return repliesWithNested;
    }
    // 获取带嵌套回复的顶级回复（支持分页）
    async findTopLevelRepliesWithNested(parentThreadId, skip = 0, limit = 10, maxDepth = 3) {
        // 获取顶级回复（分页）
        const topLevelReplies = await thread_1.default.find({
            parent_thread_id: parentThreadId,
            status: 'visible'
        })
            .sort({ created_at: 1 })
            .skip(skip)
            .limit(limit);
        if (topLevelReplies.length === 0) {
            return [];
        }
        const repliesData = topLevelReplies.map(reply => this.docToObject(reply));
        const repliesWithUser = await this.populateUserInfo(repliesData);
        // 为每个顶级回复获取其嵌套回复
        const repliesWithNested = await Promise.all(repliesWithUser.map(async (reply) => {
            const nestedReplies = await this.findNestedReplies(reply.thread_id, maxDepth, 1);
            return {
                ...reply,
                replay_tread: nestedReplies
            };
        }));
        return repliesWithNested;
    }
    // 添加点赞
    async addLike(threadId, userId) {
        const thread = await thread_1.default.findOneAndUpdate({ thread_id: threadId }, {
            $addToSet: { liked_by: userId },
            $pull: { disliked_by: userId }, // 如果之前踩过，取消踩
            $inc: { like_count: 1 },
            $set: { updated_at: new Date() } // 更新时间戳
        }, { new: true });
        return thread ? this.docToObject(thread) : null;
    }
    // 取消点赞
    async removeLike(threadId, userId) {
        const thread = await thread_1.default.findOneAndUpdate({ thread_id: threadId }, {
            $pull: { liked_by: userId },
            $inc: { like_count: -1 },
            $set: { updated_at: new Date() } // 更新时间戳
        }, { new: true });
        return thread ? this.docToObject(thread) : null;
    }
    // 添加收藏
    async addBookmark(threadId, userId) {
        const thread = await thread_1.default.findOneAndUpdate({ thread_id: threadId }, {
            $addToSet: { bookmarked_by: userId },
            $inc: { bookmark_count: 1 },
            $set: { updated_at: new Date() } // 更新时间戳
        }, { new: true });
        return thread ? this.docToObject(thread) : null;
    }
    // 取消收藏
    async removeBookmark(threadId, userId) {
        const thread = await thread_1.default.findOneAndUpdate({ thread_id: threadId }, {
            $pull: { bookmarked_by: userId },
            $inc: { bookmark_count: -1 },
            $set: { updated_at: new Date() } // 更新时间戳
        }, { new: true });
        return thread ? this.docToObject(thread) : null;
    }
    // 增加回复数
    async incrementReplyCount(threadId) {
        const thread = await thread_1.default.findOneAndUpdate({ thread_id: threadId }, {
            $inc: { reply_count: 1 },
            $set: { updated_at: new Date() } // 更新时间戳
        }, { new: true });
        return thread ? this.docToObject(thread) : null;
    }
    // 减少回复数
    async decrementReplyCount(threadId) {
        const thread = await thread_1.default.findOneAndUpdate({ thread_id: threadId }, {
            $inc: { reply_count: -1 },
            $set: { updated_at: new Date() } // 更新时间戳
        }, { new: true });
        return thread ? this.docToObject(thread) : null;
    }
    // 更新帖子内容
    async updateByThreadId(threadId, updateData) {
        const thread = await thread_1.default.findOneAndUpdate({ thread_id: threadId }, updateData, { new: true });
        return thread ? this.docToObject(thread) : null;
    }
    // 更新帖子状态
    async updateStatus(threadId, status) {
        const thread = await thread_1.default.findOneAndUpdate({ thread_id: threadId }, { status }, { new: true });
        return thread ? this.docToObject(thread) : null;
    }
    // 统计公共帖子数量
    async countPublicThreads() {
        return await thread_1.default.countDocuments({
            parent_thread_id: null,
            status: 'visible'
        });
    }
    // 统计用户帖子数量
    async countThreadsByUserId(userId) {
        return await thread_1.default.countDocuments({
            user_id: userId,
            parent_thread_id: null,
            status: { $in: ['visible', 'hidden'] }
        });
    }
    // 统计回复数量
    async countReplies(parentThreadId) {
        return await thread_1.default.countDocuments({
            parent_thread_id: parentThreadId,
            status: 'visible'
        });
    }
    // 获取用户点赞的帖子
    async findUserLikedThreads(userId, skip = 0, limit = 10) {
        const threads = await thread_1.default.find({
            liked_by: userId,
            parent_thread_id: null,
            status: 'visible'
        })
            .sort({ created_at: -1 })
            .skip(skip)
            .limit(limit);
        const threadData = threads.map(thread => this.docToObject(thread));
        return await this.populateUserInfo(threadData);
    }
    // 获取用户收藏的帖子
    async findUserBookmarkedThreads(userId, skip = 0, limit = 10) {
        const threads = await thread_1.default.find({
            bookmarked_by: userId,
            parent_thread_id: null,
            status: 'visible'
        })
            .sort({ created_at: -1 })
            .skip(skip)
            .limit(limit);
        const threadData = threads.map(thread => this.docToObject(thread));
        return await this.populateUserInfo(threadData);
    }
    // 统计用户点赞的帖子数量
    async countUserLikedThreads(userId) {
        return await thread_1.default.countDocuments({
            liked_by: userId,
            parent_thread_id: null,
            status: 'visible'
        });
    }
    // 统计用户收藏的帖子数量
    async countUserBookmarkedThreads(userId) {
        return await thread_1.default.countDocuments({
            bookmarked_by: userId,
            parent_thread_id: null,
            status: 'visible'
        });
    }
    // 获取用户的评论（回复）
    async findUserComments(userId, skip = 0, limit = 10) {
        const comments = await thread_1.default.find({
            user_id: userId,
            parent_thread_id: { $ne: null }, // 只获取回复，不包括主帖
            status: 'visible'
        })
            .sort({ created_at: -1 })
            .skip(skip)
            .limit(limit);
        const commentData = comments.map(comment => this.docToObject(comment));
        return await this.populateUserInfo(commentData);
    }
    // 统计用户评论数量
    async countUserComments(userId) {
        return await thread_1.default.countDocuments({
            user_id: userId,
            parent_thread_id: { $ne: null }, // 只统计回复，不包括主帖
            status: 'visible'
        });
    }
    // 通过ID获取帖子和用户信息
    async findByIdWithUser(threadId) {
        const thread = await thread_1.default.findOne({
            thread_id: threadId,
            status: { $ne: 'deleted' }
        });
        if (!thread)
            return null;
        const threadData = this.docToObject(thread);
        const [threadWithUser] = await this.populateUserInfo([threadData]);
        return threadWithUser;
    }
    // 获取用户收到的点赞总数
    async getUserLikesReceivedCount(userId) {
        const result = await thread_1.default.aggregate([
            {
                $match: {
                    user_id: userId,
                    status: { $in: ['visible', 'hidden'] }
                }
            },
            {
                $group: {
                    _id: null,
                    totalLikes: { $sum: '$like_count' }
                }
            }
        ]);
        return result.length > 0 ? result[0].totalLikes : 0;
    }
    // 获取用户收到的收藏总数
    async getUserBookmarksReceivedCount(userId) {
        const result = await thread_1.default.aggregate([
            {
                $match: {
                    user_id: userId,
                    status: { $in: ['visible', 'hidden'] }
                }
            },
            {
                $group: {
                    _id: null,
                    totalBookmarks: { $sum: '$bookmark_count' }
                }
            }
        ]);
        return result.length > 0 ? result[0].totalBookmarks : 0;
    }
    // 获取关注者的帖子列表
    async findFollowingThreads(userId, skip = 0, limit = 10) {
        // 首先需要获取用户关注的人的ID列表
        // 这里需要导入Follow模型
        const Follow = (await Promise.resolve().then(() => __importStar(require('../models/follow')))).default;
        // 获取用户关注的人的ID列表
        const followingList = await Follow.find({ follower_id: userId }).select('following_id');
        const followingIds = followingList.map(follow => follow.following_id);
        if (followingIds.length === 0) {
            return []; // 如果没有关注任何人，返回空数组
        }
        // 获取关注者的帖子，按时间倒序排列
        const threads = await thread_1.default.find({
            user_id: { $in: followingIds }, // 帖子作者在关注列表中
            parent_thread_id: null, // 只获取主帖，不包含评论
            status: 'visible'
        })
            .sort({ created_at: -1 }) // 按创建时间倒序
            .skip(skip)
            .limit(limit);
        const threadData = threads.map(thread => this.docToObject(thread));
        return await this.populateUserInfo(threadData);
    }
    // 统计关注者的帖子数量
    async countFollowingThreads(userId) {
        // 获取用户关注的人的ID列表
        const Follow = (await Promise.resolve().then(() => __importStar(require('../models/follow')))).default;
        const followingList = await Follow.find({ follower_id: userId }).select('following_id');
        const followingIds = followingList.map(follow => follow.following_id);
        if (followingIds.length === 0) {
            return 0;
        }
        return await thread_1.default.countDocuments({
            user_id: { $in: followingIds },
            parent_thread_id: null,
            status: 'visible'
        });
    }
    // 通过thread_id获取帖子数据
    async findByThreadId(threadId) {
        const thread = await thread_1.default.findOne({
            thread_id: threadId,
            status: { $ne: 'deleted' }
        });
        return thread ? this.docToObject(thread) : null;
    }
    // Raw query method for flexible searching
    async findRaw(query, skip = 0, limit = 0) {
        try {
            let queryBuilder = thread_1.default.find(query);
            if (skip > 0) {
                queryBuilder = queryBuilder.skip(skip);
            }
            if (limit > 0) {
                queryBuilder = queryBuilder.limit(limit);
            }
            const threads = await queryBuilder.lean();
            return threads.map(thread => ({
                ...thread,
                thread_id: thread.thread_id,
                user_id: thread.user_id,
                title: thread.title,
                content: thread.content,
                images: thread.images || [],
                parent_thread_id: thread.parent_thread_id,
                root_thread_id: thread.root_thread_id,
                reply_to_user_id: thread.reply_to_user_id,
                reply_level: thread.reply_level,
                liked_by: thread.liked_by || [],
                bookmarked_by: thread.bookmarked_by || [],
                like_count: thread.like_count || 0,
                reply_count: thread.reply_count || 0,
                bookmark_count: thread.bookmark_count || 0,
                status: thread.status,
                created_at: thread.created_at,
                updated_at: thread.updated_at
            }));
        }
        catch (error) {
            console.error('Error executing raw thread query:', error);
            throw error;
        }
    }
    // Count documents for raw query
    async countRaw(query) {
        try {
            return await thread_1.default.countDocuments(query);
        }
        catch (error) {
            console.error('Error counting threads with raw query:', error);
            throw error;
        }
    }
}
const threadRepo = new ThreadRepo();
exports.default = threadRepo;
//# sourceMappingURL=thread-repo.js.map