"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const brand_service_js_1 = __importDefault(require("../services/brand-service.js"));
const response_config_js_1 = __importDefault(require("../config/response-config.js"));
const router = express_1.default.Router();
// Configure multer for brand logo uploads
const storage = multer_1.default.diskStorage({
    destination: (_req, _file, cb) => {
        const uploadDir = path_1.default.join(process.cwd(), 'assets/brand_logo');
        console.log('Multer destination called with directory:', uploadDir);
        // Create directory if it doesn't exist
        if (!fs_1.default.existsSync(uploadDir)) {
            fs_1.default.mkdirSync(uploadDir, { recursive: true });
            console.log('Created directory:', uploadDir);
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        // Generate unique filename with timestamp
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path_1.default.extname(file.originalname);
        const filename = `brand-${uniqueSuffix}${ext}`;
        console.log('Multer filename generated:', filename);
        cb(null, filename);
    }
});
const upload = (0, multer_1.default)({
    storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
    },
    fileFilter: (_req, file, cb) => {
        const allowedTypes = /jpeg|jpg|png|gif|webp/;
        const extname = allowedTypes.test(path_1.default.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);
        if (mimetype && extname) {
            return cb(null, true);
        }
        else {
            cb(new Error('只允许上传图片文件 (jpeg, jpg, png, gif, webp)'));
        }
    }
});
router.get("/", (async (req, res) => {
    try {
        const list = req.query.list === "true";
        if (list) {
            const brands = await brand_service_js_1.default.getBrandNamesList();
            return res.json(response_config_js_1.default.success(brands, "查询成功", 200));
        }
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const name = req.query.name;
        if (name) {
            const result = await brand_service_js_1.default.getBrandsByFuzzyName(name, page, limit);
            return res.json(response_config_js_1.default.pageSuccess({
                brands: result.brands,
            }, result.totalCount, result.totalPages, page, "查询成功", 200));
        }
        const result = await brand_service_js_1.default.getAllBrands(page, limit);
        res.json(response_config_js_1.default.pageSuccess({
            brands: result.brands,
        }, result.totalCount, result.totalPages, page, "查询成功", 200));
    }
    catch (error) {
        res.status(500).json(response_config_js_1.default.error(error.message));
    }
}));
/**
 * 获取品牌分析数据
 * 返回每个品牌的产品数量、总产品数和总品牌数
 */
router.get("/analytics", (async (req, res) => {
    try {
        const analyticsData = await brand_service_js_1.default.getBrandAnalytics();
        res.json(response_config_js_1.default.success(analyticsData, "品牌分析数据获取成功", 200));
    }
    catch (error) {
        res.status(500).json(response_config_js_1.default.error(error.message));
    }
}));
/**
 * 更新所有品牌的logo_url字段
 * GET /brands/update-logo-urls
 */
router.get("/update-logo-urls", (async (req, res) => {
    try {
        const result = await brand_service_js_1.default.updateAllBrandLogoUrls();
        res.json(response_config_js_1.default.success(result, "品牌logo_url更新完成", 200));
    }
    catch (error) {
        console.error('Error updating brand logo URLs:', error);
        res.status(500).json(response_config_js_1.default.error(error.message));
    }
}));
router.get("/:id", (async (req, res) => {
    try {
        const brand = await brand_service_js_1.default.getBrandById(req.params.id);
        if (!brand) {
            return res
                .status(404)
                .json(response_config_js_1.default.error("未找到该品牌ID", 404));
        }
        res.json(response_config_js_1.default.success(brand, "查询成功", 200));
    }
    catch (error) {
        res.status(500).json(response_config_js_1.default.error(error.message));
    }
}));
/**
 * 新增brand
 */
router.post("/", (async (req, res) => {
    try {
        const { name, website_url, logo_url, desc } = req.body;
        if (!name || name.trim() === "") {
            return res
                .status(400)
                .json(response_config_js_1.default.error("品牌名称是必填项", 400));
        }
        const brandData = {
            name: name.trim(),
            website_url: website_url || "",
            logo_url: logo_url || "",
            desc: desc || "",
        };
        const brand = await brand_service_js_1.default.createBrand(brandData);
        res.status(201).json(response_config_js_1.default.success(brand, "品牌创建成功", 201));
    }
    catch (error) {
        if (error.code === 11000) {
            return res
                .status(409)
                .json(response_config_js_1.default.error("该品牌名称已存在", 409));
        }
        res.status(500).json(response_config_js_1.default.error(error.message));
    }
}));
/**
 * 修改品牌
 */
router.put("/:id", (async (req, res) => {
    try {
        const { id } = req.params;
        const { name, website_url, logo_url, desc } = req.body;
        // 检查品牌是否存在
        const existingBrand = await brand_service_js_1.default.getBrandById(id);
        if (!existingBrand) {
            return res.status(404).json(response_config_js_1.default.error("未找到该品牌", 404));
        }
        // 验证输入数据
        if (name && name.trim() === "") {
            return res
                .status(400)
                .json(response_config_js_1.default.error("品牌名称不能为空", 400));
        }
        const brandData = {};
        if (name)
            brandData.name = name.trim();
        if (website_url !== undefined)
            brandData.website_url = website_url;
        if (logo_url !== undefined)
            brandData.logo_url = logo_url;
        if (desc !== undefined)
            brandData.desc = desc;
        const updatedBrand = await brand_service_js_1.default.updateBrand(id, brandData);
        res.json(response_config_js_1.default.success(updatedBrand, "品牌更新成功", 200));
    }
    catch (error) {
        if (error.code === 11000) {
            return res
                .status(409)
                .json(response_config_js_1.default.error("该品牌名称已被使用", 409));
        }
        res.status(500).json(response_config_js_1.default.error(error.message));
    }
}));
/**
 * 删除品牌
 */
router.delete("/:id", (async (req, res) => {
    try {
        const { id } = req.params;
        // 检查品牌是否存在
        const existingBrand = await brand_service_js_1.default.getBrandById(id);
        if (!existingBrand) {
            return res.status(404).json(response_config_js_1.default.error("未找到该品牌", 404));
        }
        await brand_service_js_1.default.deleteBrand(id);
        res.json(response_config_js_1.default.success(null, "品牌删除成功", 200));
    }
    catch (error) {
        res.status(500).json(response_config_js_1.default.error(error.message));
    }
}));
/**
 * 品牌Logo上传端点 (支持文件上传和URL设置)
 * POST /brands/upload-logo
 */
router.post("/upload-logo", upload.single('image'), (async (req, res) => {
    try {
        const { brandId, imageUrl } = req.body;
        if (!brandId) {
            return res.status(400).json(response_config_js_1.default.error("品牌ID是必需的"));
        }
        // 验证品牌是否存在（如果不是临时ID）
        let existingBrand = null;
        if (brandId !== "temp") {
            existingBrand = await brand_service_js_1.default.getBrandById(brandId);
            if (!existingBrand) {
                return res.status(404).json(response_config_js_1.default.error("品牌不存在"));
            }
        }
        let finalImageUrl = null;
        if (req.file) {
            // 处理文件上传
            // 统一命名规则：assets/brand_logo/{brandId}.png
            const newFilename = `${brandId}.png`;
            const oldPath = req.file.path;
            // 确保文件保存在正确的目录中
            const uploadDir = path_1.default.join(process.cwd(), 'assets/brand_logo');
            const newPath = path_1.default.join(uploadDir, newFilename);
            console.log('Brand logo upload debug info:');
            console.log('  - Original file path:', oldPath);
            console.log('  - Target directory:', uploadDir);
            console.log('  - New file path:', newPath);
            console.log('  - Directory comparison:', path_1.default.dirname(oldPath), 'vs', uploadDir);
            // 如目标文件已存在则先删除
            if (fs_1.default.existsSync(newPath)) {
                try {
                    fs_1.default.unlinkSync(newPath);
                }
                catch { }
            }
            // 移动/重命名到目标文件
            console.log('  - Moving/Renaming file from', oldPath, 'to', newPath);
            fs_1.default.renameSync(oldPath, newPath);
            finalImageUrl = `assets/brand_logo/${newFilename}`; // 相对路径（与前端规则一致）
        }
        else if (imageUrl && typeof imageUrl === 'string') {
            // 处理直接URL设置，支持http/https和相对路径
            const trimmed = imageUrl.trim();
            const isHttp = /^https?:\/\//i.test(trimmed);
            const isRelativePath = /^(assets\/brand_logo\/|uploads\/.+)/.test(trimmed);
            const isValidImagePath = /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(trimmed);
            if (isHttp || (isRelativePath && isValidImagePath)) {
                finalImageUrl = trimmed;
            }
            else {
                return res.status(400).json(response_config_js_1.default.error("imageUrl必须是有效的HTTP链接或相对路径"));
            }
        }
        else {
            return res.status(400).json(response_config_js_1.default.error("没有上传文件或提供imageUrl"));
        }
        // 如果是现有品牌，更新Logo URL；如果是临时ID，只返回图片URL
        let updatedBrand = null;
        if (brandId !== "temp" && existingBrand) {
            const updateData = { logo_url: finalImageUrl };
            updatedBrand = await brand_service_js_1.default.updateBrand(brandId, updateData);
        }
        res.json(response_config_js_1.default.success({
            imageUrl: finalImageUrl,
            brandId,
            brand: updatedBrand
        }, brandId === "temp" ? "Logo上传成功" : "品牌Logo更新成功"));
    }
    catch (error) {
        console.error('Brand logo upload error:', error);
        res.status(500).json(response_config_js_1.default.error(error.message || "Logo上传失败"));
    }
}));
exports.default = router;
//# sourceMappingURL=brand-routes.js.map