"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const dashboard_stats_service_1 = __importDefault(require("../services/dashboard-stats-service"));
const response_data_1 = require("../utils/response-data");
const router = express_1.default.Router();
// GET /api/dashboard-stats/overview - 获取概览统计
router.get('/overview', async (req, res) => {
    try {
        const stats = await dashboard_stats_service_1.default.getOverviewStats();
        res.json(response_data_1.ResponseData.success(stats));
    }
    catch (error) {
        console.error('Get overview stats error:', error);
        res.status(500).json(response_data_1.ResponseData.error(error.message));
    }
});
// GET /api/dashboard-stats/users - 获取用户统计
router.get('/users', async (req, res) => {
    try {
        const stats = await dashboard_stats_service_1.default.getUserStats();
        res.json(response_data_1.ResponseData.success(stats));
    }
    catch (error) {
        console.error('Get user stats error:', error);
        res.status(500).json(response_data_1.ResponseData.error(error.message));
    }
});
// GET /api/dashboard-stats/content - 获取内容统计
router.get('/content', async (req, res) => {
    try {
        const stats = await dashboard_stats_service_1.default.getContentStats();
        res.json(response_data_1.ResponseData.success(stats));
    }
    catch (error) {
        console.error('Get content stats error:', error);
        res.status(500).json(response_data_1.ResponseData.error(error.message));
    }
});
// GET /api/dashboard-stats/engagement - 获取互动统计
router.get('/engagement', async (req, res) => {
    try {
        const stats = await dashboard_stats_service_1.default.getEngagementStats();
        res.json(response_data_1.ResponseData.success(stats));
    }
    catch (error) {
        console.error('Get engagement stats error:', error);
        res.status(500).json(response_data_1.ResponseData.error(error.message));
    }
});
// GET /api/dashboard-stats/trends - 获取趋势数据
router.get('/trends', async (req, res) => {
    try {
        const days = parseInt(req.query.days) || 7;
        const stats = await dashboard_stats_service_1.default.getTrendStats(days);
        res.json(response_data_1.ResponseData.success(stats));
    }
    catch (error) {
        console.error('Get trend stats error:', error);
        res.status(500).json(response_data_1.ResponseData.error(error.message));
    }
});
// GET /api/dashboard-stats/popular - 获取热门数据
router.get('/popular', async (req, res) => {
    try {
        const stats = await dashboard_stats_service_1.default.getPopularStats();
        res.json(response_data_1.ResponseData.success(stats));
    }
    catch (error) {
        console.error('Get popular stats error:', error);
        res.status(500).json(response_data_1.ResponseData.error(error.message));
    }
});
exports.default = router;
//# sourceMappingURL=dashboard-stats-routes.js.map