"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const font_user_services_1 = __importDefault(require("../services/font-user-services"));
const response_config_1 = __importDefault(require("../config/response-config"));
const router = express_1.default.Router();
// In CommonJS, __dirname is available globally
// Configure multer for file uploads
const storage = multer_1.default.diskStorage({
    destination: (_req, _file, cb) => {
        const uploadDir = path_1.default.join(__dirname, '../../uploads/users');
        // Create directory if it doesn't exist
        if (!fs_1.default.existsSync(uploadDir)) {
            fs_1.default.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        // 在multer处理时，req.body可能还没有完全解析
        // 我们先使用临时文件名，稍后在API端点中重命名
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path_1.default.extname(file.originalname);
        const tempFilename = `temp-${uniqueSuffix}${ext}`;
        cb(null, tempFilename);
    }
});
const upload = (0, multer_1.default)({
    storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
    },
    fileFilter: (_req, file, cb) => {
        const allowedTypes = /jpeg|jpg|png|gif|webp/;
        const extname = allowedTypes.test(path_1.default.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);
        if (mimetype && extname) {
            return cb(null, true);
        }
        else {
            cb(new Error('只允许上传图片文件 (jpeg, jpg, png, gif, webp)'));
        }
    }
});
// 获取所有用户
router.get("/", (async (req, res) => {
    try {
        const result = await font_user_services_1.default.getAllUsers();
        res.json(response_config_1.default.success(result));
    }
    catch (error) {
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
// 初始化用户数据 - 移到这里避免与 /:id 路由冲突
router.get("/initialize", (async (req, res) => {
    try {
        const result = await font_user_services_1.default.createInitialUsers();
        res.json(response_config_1.default.success(result));
    }
    catch (error) {
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
// 根据ID获取用户基本信息（用于评论显示）- 必须在 /:id 之前定义
router.get("/:id/basic", (async (req, res) => {
    try {
        const { id } = req.params;
        const user = await font_user_services_1.default.getUserById(id);
        if (user) {
            // 只返回基本信息
            const basicInfo = {
                id: user.id,
                username: user.username,
                avatar: user.avatar
            };
            res.json(response_config_1.default.success(basicInfo));
        }
        else {
            // 用户不存在时返回匿名用户信息，而不是错误
            const anonymousInfo = {
                id: id,
                username: '匿名用户',
                avatar: null
            };
            res.json(response_config_1.default.success(anonymousInfo));
        }
    }
    catch (error) {
        // 即使出错也返回匿名用户信息
        const anonymousInfo = {
            id: req.params.id,
            username: '匿名用户',
            avatar: null
        };
        res.json(response_config_1.default.success(anonymousInfo));
    }
}));
// 更新用户资料 - 也要在 /:id 之前定义
router.put("/:id/profile", (async (req, res) => {
    try {
        const { id } = req.params;
        const profileData = req.body;
        // 验证bio长度（最多5行）
        if (profileData.bio) {
            const lines = profileData.bio.split('\n');
            if (lines.length > 5) {
                return res.status(400).json(response_config_1.default.error("简介最多只能有5行"));
            }
        }
        // 验证生日格式
        if (profileData.birthday && !/^\d{4}-\d{2}-\d{2}$/.test(profileData.birthday)) {
            return res.status(400).json(response_config_1.default.error("生日格式应为 YYYY-MM-DD"));
        }
        // 验证性别
        if (profileData.gender && !['male', 'female', 'other'].includes(profileData.gender)) {
            return res.status(400).json(response_config_1.default.error("性别值无效"));
        }
        // 更新 updatedAt 时间戳
        profileData.updatedAt = new Date();
        const result = await font_user_services_1.default.updateUser(id, profileData);
        if (result) {
            res.json(response_config_1.default.success(result));
        }
        else {
            res.status(404).json(response_config_1.default.error("用户不存在"));
        }
    }
    catch (error) {
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
// 根据ID获取用户 - 放在具体路由之后
router.get("/:id", (async (req, res) => {
    try {
        const { id } = req.params;
        const result = await font_user_services_1.default.getUserById(id);
        if (result) {
            res.json(response_config_1.default.success(result));
        }
        else {
            res.status(404).json(response_config_1.default.error("用户不存在"));
        }
    }
    catch (error) {
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
// 创建用户
router.post("/", (async (req, res) => {
    try {
        const userData = { ...req.body };
        // 处理前端传入的role对象
        if (userData.role && typeof userData.role === 'object' && userData.role.id) {
            userData.role = userData.role.id;
        }
        const result = await font_user_services_1.default.createUser(userData);
        res.status(201).json(response_config_1.default.success(result));
    }
    catch (error) {
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
// 更新用户
router.put("/:id", (async (req, res) => {
    try {
        const { id } = req.params;
        const userData = { ...req.body };
        // 处理前端传入的role对象
        if (userData.role && typeof userData.role === 'object' && userData.role.id) {
            userData.role = userData.role.id;
        }
        const result = await font_user_services_1.default.updateUser(id, userData);
        if (result) {
            res.json(response_config_1.default.success(result));
        }
        else {
            res.status(404).json(response_config_1.default.error("用户不存在"));
        }
    }
    catch (error) {
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
// 删除用户
router.delete("/:id", (async (req, res) => {
    try {
        const { id } = req.params;
        const result = await font_user_services_1.default.deleteUser(id);
        if (result) {
            res.json(response_config_1.default.success(result));
        }
        else {
            res.status(404).json(response_config_1.default.error("用户不存在"));
        }
    }
    catch (error) {
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
// Image upload endpoint (supports file or URL and persists to user)
router.post("/upload-image", upload.single('image'), (async (req, res) => {
    try {
        const { type, userId, imageUrl } = req.body;
        if (!type || !['avatar', 'background'].includes(type)) {
            return res.status(400).json(response_config_1.default.error("图片类型必须是avatar或background"));
        }
        // 决定更新的字段名
        const field = type === 'avatar' ? 'avatar' : 'backgroundImage';
        let finalImageUrl = null;
        if (req.file) {
            // 处理文件上传
            const ext = path_1.default.extname(req.file.originalname);
            const baseName = userId
                ? `${userId}_${type}`
                : `user-${Date.now()}-${Math.round(Math.random() * 1e9)}_${type}`;
            const newFilename = `${baseName}${ext}`;
            const oldPath = req.file.path;
            const newPath = path_1.default.join(path_1.default.dirname(oldPath), newFilename);
            fs_1.default.renameSync(oldPath, newPath);
            finalImageUrl = `uploads/users/${newFilename}`; // 相对路径
        }
        else if (imageUrl && typeof imageUrl === 'string') {
            // 处理直接URL设置，仅允许http/https
            const trimmed = imageUrl.trim();
            const isHttp = /^https?:\/\//i.test(trimmed);
            if (!isHttp) {
                return res.status(400).json(response_config_1.default.error("imageUrl必须是以http或https开头的有效链接"));
            }
            finalImageUrl = trimmed;
        }
        else {
            return res.status(400).json(response_config_1.default.error("没有上传文件或提供imageUrl"));
        }
        // 如果提供了 userId，同步更新用户记录；否则仅返回上传结果
        if (userId) {
            const updated = await font_user_services_1.default.updateUser(userId, { [field]: finalImageUrl, updatedAt: new Date() });
            if (!updated) {
                return res.status(404).json(response_config_1.default.error("用户不存在"));
            }
            return res.json(response_config_1.default.success({
                imageUrl: finalImageUrl,
                type,
                userId,
                field,
                user: updated,
            }));
        }
        // 未提供 userId 的情况：返回上传后的图片地址，前端可在创建时带回字段
        return res.json(response_config_1.default.success({
            imageUrl: finalImageUrl,
            type,
            field,
        }));
    }
    catch (error) {
        console.error('Upload error:', error);
        res.status(500).json(response_config_1.default.error(error.message || "图片上传失败"));
    }
}));
exports.default = router;
//# sourceMappingURL=font-user-routes.js.map