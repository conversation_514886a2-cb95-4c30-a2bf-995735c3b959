"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const product_service_1 = __importDefault(require("../services/product-service"));
const brand_service_1 = __importDefault(require("../services/brand-service"));
const image_service_1 = __importDefault(require("../services/image-service"));
const response_config_1 = __importDefault(require("../config/response-config"));
const router = express_1.default.Router();
// Configure multer for product image uploads
const storage = multer_1.default.diskStorage({
    destination: (_req, _file, cb) => {
        const uploadDir = path_1.default.join(process.cwd(), 'assets/product_iamges');
        console.log('Multer destination called with directory:', uploadDir);
        // Create directory if it doesn't exist
        if (!fs_1.default.existsSync(uploadDir)) {
            fs_1.default.mkdirSync(uploadDir, { recursive: true });
            console.log('Created directory:', uploadDir);
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        // Generate unique filename with timestamp
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path_1.default.extname(file.originalname);
        const filename = `product-${uniqueSuffix}${ext}`;
        console.log('Multer filename generated:', filename);
        cb(null, filename);
    }
});
const upload = (0, multer_1.default)({
    storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
    },
    fileFilter: (_req, file, cb) => {
        const allowedTypes = /jpeg|jpg|png|gif|webp/;
        const extname = allowedTypes.test(path_1.default.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);
        if (mimetype && extname) {
            return cb(null, true);
        }
        else {
            cb(new Error('只允许上传图片文件 (jpeg, jpg, png, gif, webp)'));
        }
    }
});
router.get("/", (async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const name = req.query.name;
        if (name) {
            const result = await product_service_1.default.getProductsByFuzzyName(name, page, limit);
            return res.json(response_config_1.default.pageSuccess({
                products: result.products,
            }, result.totalCount, result.totalPages, page, "查询成功", 200));
        }
        const result = await product_service_1.default.getProducts(page, limit);
        res.json(response_config_1.default.pageSuccess({
            products: result.products,
        }, result.totalCount, result.totalPages, page, "查询成功", 200));
    }
    catch (error) {
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
/**
 * query: page,limit,sortBy,sortOrder
 */
router.get("/ranking", (async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const sortBy = req.query.sortBy || 'quality';
        const sortOrder = req.query.sortOrder || 'desc';
        const name = req.query.name;
        const validSortFields = ['quality', 'protein', 'fat', 'carbs', 'calories', 'quality_ingredients', 'questionable_ingredients'];
        const validSortOrders = ['asc', 'desc'];
        if (!validSortFields.includes(sortBy)) {
            return res.status(400).json(response_config_1.default.error(`Invalid sort field. Valid options are: ${validSortFields.join(', ')}`, 400));
        }
        if (!validSortOrders.includes(sortOrder)) {
            return res.status(400).json(response_config_1.default.error("Invalid sort order. Use 'asc' or 'desc'", 400));
        }
        const result = await product_service_1.default.getProductRanking(name, sortBy, sortOrder, page, limit);
        res.json(response_config_1.default.pageSuccess({
            sortBy: result.sortBy,
            sortOrder: result.sortOrder,
            products: result.products,
        }, result.totalCount, result.totalPages, page, "查询成功", 200));
    }
    catch (error) {
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
/**
 * 创建新产品
 */
router.post("/", (async (req, res) => {
    try {
        const { name, brand, product_type, image_url, product_url, allergen_ingredients, est_calories, quality_ingredients, questionable_ingredients, guaranteed_analysis, dry_matter_analysis } = req.body;
        // 验证必填字段
        if (!name || name.trim() === '') {
            return res.status(400).json(response_config_1.default.error("产品名称是必填项", 400));
        }
        if (!brand || brand.trim() === '') {
            return res.status(400).json(response_config_1.default.error("品牌名称是必填项", 400));
        }
        // 验证品牌是否存在
        const existingBrand = await brand_service_1.default.getBrandByName(brand);
        if (!existingBrand) {
            return res.status(400).json(response_config_1.default.error("指定的品牌不存在，请先创建该品牌或选择现有品牌", 400));
        }
        // 准备产品数据
        const productData = {
            _id: req.body._id || new Date().getTime().toString(), // 如果没有提供ID，生成一个基于时间戳的ID
            name: name.trim(),
            brand: brand.trim(),
            product_type,
            image_url,
            product_url,
            allergen_ingredients,
            est_calories,
            quality_ingredients,
            questionable_ingredients,
            guaranteed_analysis,
            dry_matter_analysis
        };
        const product = await product_service_1.default.createProduct(productData);
        res.status(201).json(response_config_1.default.success(product, "产品创建成功", 201));
    }
    catch (error) {
        if (error.code === 11000) {
            return res.status(409).json(response_config_1.default.error("产品ID已存在", 409));
        }
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
/**
 * 更新产品
 * PUT /products/:id
 */
router.put("/:id", (async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;
        // 验证产品ID
        if (!id || id.length < 10) {
            return res.status(400).json(response_config_1.default.error("无效的产品ID", 400));
        }
        // 检查产品是否存在
        const existingProduct = await product_service_1.default.getProductById(id);
        if (!existingProduct) {
            return res.status(404).json(response_config_1.default.error("产品不存在", 404));
        }
        // 如果更新了品牌，验证品牌是否存在
        if (updateData.brand && updateData.brand.trim() !== '') {
            const existingBrand = await brand_service_1.default.getBrandByName(updateData.brand.trim());
            if (!existingBrand) {
                return res.status(400).json(response_config_1.default.error("指定的品牌不存在，请先创建该品牌或选择现有品牌", 400));
            }
        }
        // 更新产品
        const updatedProduct = await product_service_1.default.updateProduct(id, updateData);
        res.json(response_config_1.default.success(updatedProduct, "产品更新成功"));
    }
    catch (error) {
        console.error('Product update error:', error);
        res.status(500).json(response_config_1.default.error(error.message || "产品更新失败"));
    }
}));
/**
 * 删除产品
 * DELETE /products/:id
 */
router.delete("/:id", (async (req, res) => {
    try {
        const { id } = req.params;
        // 验证产品ID
        if (!id || id.length < 10) {
            return res.status(400).json(response_config_1.default.error("无效的产品ID", 400));
        }
        // 检查产品是否存在
        const existingProduct = await product_service_1.default.getProductById(id);
        if (!existingProduct) {
            return res.status(404).json(response_config_1.default.error("产品不存在", 404));
        }
        // 删除产品
        await product_service_1.default.deleteProduct(id);
        res.json(response_config_1.default.success(null, "产品删除成功"));
    }
    catch (error) {
        console.error('Product delete error:', error);
        res.status(500).json(response_config_1.default.error(error.message || "产品删除失败"));
    }
}));
/**
 * 预览批量更新操作（不实际执行更新）
 * GET /products/preview-image-url-update
 * 显示哪些产品将被更新
 */
router.get("/preview-image-url-update", (async (req, res) => {
    try {
        console.log("预览批量更新产品图片URL...");
        // 获取所有产品
        const allProducts = await product_service_1.default.getAllProducts();
        const updatePreview = allProducts.map(product => {
            const currentUrl = product.image_url || '';
            const isAlreadyLocal = currentUrl.startsWith('assets/product_iamges/');
            const newUrl = `assets/product_iamges/${product._id}.png`;
            return {
                productId: product._id,
                name: product.name,
                brand: product.brand,
                currentImageUrl: currentUrl,
                newImageUrl: newUrl,
                needsUpdate: !isAlreadyLocal,
                status: isAlreadyLocal ? '已是本地路径' : '需要更新'
            };
        });
        const needsUpdateCount = updatePreview.filter(item => item.needsUpdate).length;
        const alreadyLocalCount = updatePreview.filter(item => !item.needsUpdate).length;
        res.json(response_config_1.default.success({
            totalProducts: allProducts.length,
            needsUpdateCount,
            alreadyLocalCount,
            preview: updatePreview.slice(0, 20), // 只返回前20个作为预览
            summary: {
                totalProducts: allProducts.length,
                willBeUpdated: needsUpdateCount,
                alreadyLocal: alreadyLocalCount
            }
        }, "预览批量更新操作", 200));
    }
    catch (error) {
        console.error('预览批量更新失败:', error);
        res.status(500).json(response_config_1.default.error(`预览失败: ${error.message}`));
    }
}));
/**
 * 批量更新所有产品的图片URL
 * PUT /products/batch-update-image-urls
 * 将所有产品的image_url从外部URL更新为本地相对路径
 */
router.get("/batch-update-image-urls", (async (req, res) => {
    try {
        console.log("开始批量更新产品图片URL...");
        // 获取所有产品
        const allProducts = await product_service_1.default.getAllProducts();
        console.log(`找到 ${allProducts.length} 个产品需要更新`);
        let updatedCount = 0;
        let skippedCount = 0;
        const errors = [];
        // 遍历所有产品并更新image_url
        for (const product of allProducts) {
            try {
                // 检查当前image_url是否已经是本地路径
                if (product.image_url && product.image_url.startsWith('assets/product_iamges/')) {
                    console.log(`产品 ${product._id} 的图片URL已经是本地路径，跳过更新`);
                    skippedCount++;
                    continue;
                }
                // 生成新的本地图片URL（相对路径）
                const newImageUrl = `assets/product_iamges/${product._id}.png`;
                // 更新产品的image_url
                await product_service_1.default.updateProduct(product._id, {
                    image_url: newImageUrl
                });
                console.log(`已更新产品 ${product._id}: ${product.name} -> ${newImageUrl}`);
                updatedCount++;
            }
            catch (updateError) {
                const errorMsg = `更新产品 ${product._id} 失败: ${updateError.message}`;
                console.error(errorMsg);
                errors.push(errorMsg);
            }
        }
        console.log(`批量更新完成: ${updatedCount} 个更新, ${skippedCount} 个跳过, ${errors.length} 个错误`);
        res.json(response_config_1.default.success({
            totalProducts: allProducts.length,
            updatedCount,
            skippedCount,
            errorCount: errors.length,
            errors: errors.slice(0, 10), // 只返回前10个错误信息
            message: `成功更新 ${updatedCount} 个产品的图片URL`
        }, "批量更新图片URL完成", 200));
    }
    catch (error) {
        console.error('批量更新图片URL失败:', error);
        res.status(500).json(response_config_1.default.error(`批量更新失败: ${error.message}`));
    }
}));
/**
 * 一次性迁移：将 uploads/products 中按旧规则命名的文件
 * 移动/重命名到 assets/product_iamges 下，文件名规则 {productId}.png
 * GET /products/migrate-uploads-to-assets
 */
router.get("/migrate-uploads-to-assets", (async (_req, res) => {
    try {
        const uploadsDir = path_1.default.join(process.cwd(), 'uploads/products');
        const assetsDir = path_1.default.join(process.cwd(), 'assets/product_iamges');
        if (!fs_1.default.existsSync(uploadsDir)) {
            return res.json(response_config_1.default.success({ moved: 0, skipped: 0 }, "无可迁移的上传目录"));
        }
        if (!fs_1.default.existsSync(assetsDir)) {
            fs_1.default.mkdirSync(assetsDir, { recursive: true });
        }
        const files = fs_1.default.readdirSync(uploadsDir);
        let moved = 0;
        let skipped = 0;
        const errors = [];
        // 仅处理旧规则类似 1234567890_product.png 的文件
        const pattern = /^(\d+)_product\.(png)$/i;
        for (const filename of files) {
            try {
                const match = filename.match(pattern);
                if (!match) {
                    skipped++;
                    continue;
                }
                const productId = match[1];
                const srcPath = path_1.default.join(uploadsDir, filename);
                const destPath = path_1.default.join(assetsDir, `${productId}.png`);
                if (fs_1.default.existsSync(destPath)) {
                    // 已存在目标文件则跳过
                    skipped++;
                    continue;
                }
                fs_1.default.renameSync(srcPath, destPath);
                moved++;
            }
            catch (e) {
                errors.push(`${filename}: ${e.message}`);
            }
        }
        res.json(response_config_1.default.success({ moved, skipped, errorCount: errors.length, errors: errors.slice(0, 10) }, "迁移完成"));
    }
    catch (error) {
        console.error('迁移上传图片到assets失败:', error);
        res.status(500).json(response_config_1.default.error(error.message || "迁移失败"));
    }
}));
/**
 * Get a product by ID
 * 注意：这个动态路由必须放在所有静态路由之后
 */
router.get("/:id", (async (req, res) => {
    try {
        const { id } = req.params;
        // 检查是否是特殊路径，如果是则跳过
        if (id === 'preview-image-url-update' || id === 'batch-update-image-urls' || id === 'images') {
            return res.status(404).json(response_config_1.default.error("Route not found", 404));
        }
        // Validate ID format
        if (!id || id.length < 10) {
            return res.status(400).json(response_config_1.default.error("Invalid product ID format", 400));
        }
        // Fetch product by ID
        const product = await product_service_1.default.getProductById(id);
        // Return 404 if product not found
        if (!product) {
            return res.status(404).json(response_config_1.default.error("Product not found", 404));
        }
        // Return the product
        res.json(response_config_1.default.success(product, "产品查询成功", 200));
    }
    catch (error) {
        console.error(`Error fetching product: ${error.message}`);
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
/**
 * Batch get product images by IDs
 * POST /products/images
 * Body: { productIds: string[], page?: number, limit?: number }
 */
router.post("/images", (async (req, res) => {
    try {
        const { productIds, page = 1, limit = 10 } = req.body;
        // Validation
        if (!productIds || !Array.isArray(productIds)) {
            return res.status(400).json(response_config_1.default.error("productIds must be an array", 400));
        }
        if (productIds.length === 0) {
            return res.status(400).json(response_config_1.default.error("productIds array cannot be empty", 400));
        }
        if (productIds.length > 100) {
            return res.status(400).json(response_config_1.default.error("Maximum 100 product IDs allowed per request", 400));
        }
        const pageNum = parseInt(page) || 1;
        const limitNum = parseInt(limit) || 10;
        if (pageNum < 1) {
            return res.status(400).json(response_config_1.default.error("Page must be greater than 0", 400));
        }
        if (limitNum < 1 || limitNum > 50) {
            return res.status(400).json(response_config_1.default.error("Limit must be between 1 and 50", 400));
        }
        const result = await image_service_1.default.getProductImages(productIds, pageNum, limitNum);
        res.json(response_config_1.default.pageSuccess({
            images: result.images
        }, result.totalCount, result.totalPages, result.currentPage, "图片查询成功", 200));
    }
    catch (error) {
        console.error('Error in batch image retrieval:', error);
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
/**
 * Get images for a specific brand
 * GET /products/images/brand/:brandName
 */
router.get("/images/brand/:brandName", (async (req, res) => {
    try {
        const { brandName } = req.params;
        if (!brandName || brandName.trim() === '') {
            return res.status(400).json(response_config_1.default.error("Brand name is required", 400));
        }
        const images = await image_service_1.default.getBrandImages(brandName.trim());
        res.json(response_config_1.default.success({
            brand: brandName,
            images,
            count: images.length
        }, "品牌图片查询成功", 200));
    }
    catch (error) {
        console.error('Error getting brand images:', error);
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
/**
 * Get all available images
 * GET /products/images/all
 */
router.get("/images/all", (async (req, res) => {
    try {
        const images = await image_service_1.default.getAllImages();
        res.json(response_config_1.default.success({
            images,
            count: images.length
        }, "所有图片查询成功", 200));
    }
    catch (error) {
        console.error('Error getting all images:', error);
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
/**
 * 获取可导出的字段列表
 * GET /products/export/fields
 */
router.get("/export/fields", (async (req, res) => {
    try {
        const fields = product_service_1.default.getExportableFields();
        res.json(response_config_1.default.success(fields, "获取可导出字段成功", 200));
    }
    catch (error) {
        console.error('Error getting exportable fields:', error);
        res.status(500).json(response_config_1.default.error(error.message));
    }
}));
/**
 * 导出产品数据为Excel
 * POST /products/export
 * Body: {
 *   searchName?: string,
 *   selectedFields?: string[],
 *   isRankingMode?: boolean,
 *   sortBy?: string,
 *   sortOrder?: string
 * }
 */
router.post("/export", (async (req, res) => {
    try {
        const { searchName, selectedFields, isRankingMode = false, sortBy = 'quality', sortOrder = 'desc' } = req.body;
        // 验证排序字段
        const validSortFields = ['quality', 'protein', 'fat', 'carbs', 'calories', 'quality_ingredients', 'questionable_ingredients'];
        const validSortOrders = ['asc', 'desc'];
        if (isRankingMode && !validSortFields.includes(sortBy)) {
            return res.status(400).json(response_config_1.default.error(`Invalid sort field. Valid options are: ${validSortFields.join(', ')}`, 400));
        }
        if (isRankingMode && !validSortOrders.includes(sortOrder)) {
            return res.status(400).json(response_config_1.default.error("Invalid sort order. Use 'asc' or 'desc'", 400));
        }
        // 验证选择的字段并处理可能的字符串情况
        let fieldsArray = [];
        if (selectedFields) {
            if (Array.isArray(selectedFields)) {
                fieldsArray = selectedFields;
            }
            else if (typeof selectedFields === 'string') {
                // 如果是逗号分隔的字符串，拆分成数组
                if (selectedFields.includes(',')) {
                    fieldsArray = selectedFields.split(',').map(f => f.trim());
                }
                else {
                    // 单个字段
                    fieldsArray = [selectedFields];
                }
            }
        }
        console.log('处理后的字段数组:', fieldsArray);
        if (fieldsArray.length === 0) {
            // 如果没有有效字段，使用默认字段
            console.log('使用默认字段');
        }
        console.log('开始导出产品数据:', { searchName, selectedFields: fieldsArray, isRankingMode, sortBy, sortOrder });
        const result = await product_service_1.default.exportProductsToExcel(searchName, fieldsArray, isRankingMode, sortBy, sortOrder);
        // 设置响应头
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(result.filename)}"`);
        res.setHeader('Content-Length', result.buffer.length);
        console.log(`导出完成: ${result.totalCount} 条记录, 文件名: ${result.filename}`);
        // 发送Excel文件
        res.send(result.buffer);
    }
    catch (error) {
        console.error('Error exporting products:', error);
        res.status(500).json(response_config_1.default.error(`导出失败: ${error.message}`));
    }
}));
/**
 * 产品图片上传端点 (支持文件上传和URL设置)
 * POST /products/upload-image
 */
router.post("/upload-image", upload.single('image'), (async (req, res) => {
    try {
        const { productId, imageUrl } = req.body;
        if (!productId) {
            return res.status(400).json(response_config_1.default.error("产品ID是必需的"));
        }
        // 验证产品是否存在（如果不是临时ID）
        let existingProduct = null;
        if (productId !== "temp") {
            existingProduct = await product_service_1.default.getProductById(productId);
            if (!existingProduct) {
                return res.status(404).json(response_config_1.default.error("产品不存在"));
            }
        }
        let finalImageUrl = null;
        if (req.file) {
            // 处理文件上传
            // 统一命名规则：assets/product_iamges/{productId}.png
            const newFilename = `${productId}.png`;
            const oldPath = req.file.path;
            // 确保文件保存在正确的目录中
            const uploadDir = path_1.default.join(process.cwd(), 'assets/product_iamges');
            const newPath = path_1.default.join(uploadDir, newFilename);
            console.log('Product upload debug info:');
            console.log('  - Original file path:', oldPath);
            console.log('  - Target directory:', uploadDir);
            console.log('  - New file path:', newPath);
            console.log('  - Directory comparison:', path_1.default.dirname(oldPath), 'vs', uploadDir);
            // 如目标文件已存在则先删除，避免重名错误
            if (fs_1.default.existsSync(newPath)) {
                try {
                    fs_1.default.unlinkSync(newPath);
                }
                catch { }
            }
            // 移动/重命名到目标文件（即使在同一目录也需要重命名）
            console.log('  - Moving/Renaming file from', oldPath, 'to', newPath);
            fs_1.default.renameSync(oldPath, newPath);
            finalImageUrl = `assets/product_iamges/${newFilename}`; // 相对路径（与前端规则一致）
        }
        else if (imageUrl && typeof imageUrl === 'string') {
            // 处理直接URL设置，支持http/https和相对路径
            const trimmed = imageUrl.trim();
            const isHttp = /^https?:\/\//i.test(trimmed);
            const isRelativePath = /^(assets\/product_iamges\/|uploads\/.+)/.test(trimmed);
            const isValidImagePath = /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(trimmed);
            if (isHttp || (isRelativePath && isValidImagePath)) {
                finalImageUrl = trimmed;
            }
            else {
                return res.status(400).json(response_config_1.default.error("imageUrl必须是有效的HTTP链接或相对路径"));
            }
        }
        else {
            return res.status(400).json(response_config_1.default.error("没有上传文件或提供imageUrl"));
        }
        // 如果是现有产品，更新图片URL；如果是临时ID，只返回图片URL
        let updatedProduct = null;
        if (productId !== "temp" && existingProduct) {
            const updateData = { image_url: finalImageUrl };
            updatedProduct = await product_service_1.default.updateProduct(productId, updateData);
        }
        res.json(response_config_1.default.success({
            imageUrl: finalImageUrl,
            productId,
            product: updatedProduct
        }, productId === "temp" ? "图片上传成功" : "产品图片更新成功"));
    }
    catch (error) {
        console.error('Product image upload error:', error);
        res.status(500).json(response_config_1.default.error(error.message || "图片上传失败"));
    }
}));
exports.default = router;
//# sourceMappingURL=product-routes.js.map