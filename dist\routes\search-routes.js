"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const search_service_js_1 = require("../services/search-service.js");
const response_config_js_1 = __importDefault(require("../config/response-config.js"));
const router = express_1.default.Router();
/**
 * 实时搜索预览
 * GET /api/search/preview?q=搜索词
 */
router.get('/preview', (async (req, res) => {
    try {
        const query = req.query.q;
        if (!query || query.trim().length === 0) {
            return res.json(response_config_js_1.default.success({
                products: [],
                brands: []
            }));
        }
        const results = await search_service_js_1.searchService.searchPreview(query.trim());
        res.json(response_config_js_1.default.success(results, "搜索预览成功"));
    }
    catch (error) {
        console.error('Search preview error:', error);
        res.status(500).json(response_config_js_1.default.error(error.message || '搜索预览失败', 500));
    }
}));
/**
 * 详细搜索结果
 * GET /api/search/detailed?q=搜索词&page=1&limit=20&category=all&sortBy=relevance
 */
router.get('/detailed', (async (req, res) => {
    try {
        const query = req.query.q;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        if (!query || query.trim().length === 0) {
            return res.json(response_config_js_1.default.success({
                products: [],
                brands: [],
                threads: [],
                totalCount: 0,
                totalPages: 0,
                currentPage: page
            }, "搜索结果为空"));
        }
        const results = await search_service_js_1.searchService.searchDetailed(query.trim(), page, limit);
        res.json(response_config_js_1.default.success({
            ...results,
            currentPage: page
        }, "详细搜索成功"));
    }
    catch (error) {
        console.error('Search detailed error:', error);
        res.status(500).json(response_config_js_1.default.error(error.message || '详细搜索失败', 500));
    }
}));
/**
 * 获取热门搜索词
 * GET /api/search/hot-searches
 */
router.get('/hot-searches', (async (_req, res) => {
    try {
        const hotSearches = await search_service_js_1.searchService.getHotSearches();
        res.json(response_config_js_1.default.success(hotSearches, "获取热门搜索成功"));
    }
    catch (error) {
        console.error('Get hot searches error:', error);
        res.status(500).json(response_config_js_1.default.error(error.message || '获取热门搜索失败', 500));
    }
}));
/**
 * 获取搜索建议
 * GET /api/search/suggestions?q=搜索词
 */
router.get('/suggestions', (async (req, res) => {
    try {
        const query = req.query.q;
        if (!query || query.trim().length === 0) {
            return res.json(response_config_js_1.default.success([], "搜索建议为空"));
        }
        const suggestions = await search_service_js_1.searchService.getSearchSuggestions(query.trim());
        res.json(response_config_js_1.default.success(suggestions, "获取搜索建议成功"));
    }
    catch (error) {
        console.error('Get search suggestions error:', error);
        res.status(500).json(response_config_js_1.default.error(error.message || '获取搜索建议失败', 500));
    }
}));
/**
 * 搜索产品 - 支持无限滚动
 * GET /api/search/products?q=搜索词&page=1&limit=20
 */
router.get('/products', (async (req, res) => {
    try {
        const query = req.query.q;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        if (!query || query.trim().length === 0) {
            return res.json(response_config_js_1.default.success({
                products: [],
                totalCount: 0,
                totalPages: 0,
                currentPage: page
            }, "搜索结果为空"));
        }
        const results = await search_service_js_1.searchService.searchProducts(query.trim(), page, limit);
        res.json(response_config_js_1.default.success({
            products: results.products,
            totalCount: results.totalCount,
            totalPages: results.totalPages,
            currentPage: page
        }, "产品搜索成功"));
    }
    catch (error) {
        console.error('Search products error:', error);
        res.status(500).json(response_config_js_1.default.error(error.message || '产品搜索失败', 500));
    }
}));
/**
 * 搜索品牌 - 支持无限滚动
 * GET /api/search/brands?q=搜索词&page=1&limit=20
 */
router.get('/brands', (async (req, res) => {
    try {
        const query = req.query.q;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        if (!query || query.trim().length === 0) {
            return res.json(response_config_js_1.default.success({
                brands: [],
                totalCount: 0,
                totalPages: 0,
                currentPage: page
            }, "搜索结果为空"));
        }
        const results = await search_service_js_1.searchService.searchBrands(query.trim(), page, limit);
        res.json(response_config_js_1.default.success({
            brands: results.brands,
            totalCount: results.totalCount,
            totalPages: results.totalPages,
            currentPage: page
        }, "品牌搜索成功"));
    }
    catch (error) {
        console.error('Search brands error:', error);
        res.status(500).json(response_config_js_1.default.error(error.message || '品牌搜索失败', 500));
    }
}));
/**
 * 搜索帖子 - 支持无限滚动
 * GET /api/search/threads?q=搜索词&page=1&limit=20
 */
router.get('/threads', (async (req, res) => {
    try {
        const query = req.query.q;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        if (!query || query.trim().length === 0) {
            return res.json(response_config_js_1.default.success({
                threads: [],
                totalCount: 0,
                totalPages: 0,
                currentPage: page
            }, "搜索结果为空"));
        }
        const results = await search_service_js_1.searchService.searchThreadsWithPagination(query.trim(), page, limit);
        res.json(response_config_js_1.default.success({
            threads: results.threads,
            totalCount: results.totalCount,
            totalPages: results.totalPages,
            currentPage: page
        }, "帖子搜索成功"));
    }
    catch (error) {
        console.error('Search threads error:', error);
        res.status(500).json(response_config_js_1.default.error(error.message || '帖子搜索失败', 500));
    }
}));
/**
 * 高级产品筛选 - 支持多维度筛选
 * GET /api/search/products/filtered?q=搜索词&brands[]=品牌1&productTypes[]=类型1&proteinMin=20&proteinMax=40&page=1&limit=20&sortBy=quality&sortOrder=desc
 */
router.get('/products/filtered', (async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const sortBy = req.query.sortBy || 'quality';
        const sortOrder = req.query.sortOrder || 'desc';
        // 解析筛选参数
        const filters = {
            // 基础筛选
            searchQuery: req.query.q,
            brands: Array.isArray(req.query.brands) ? req.query.brands :
                req.query.brands ? [req.query.brands] : [],
            productTypes: Array.isArray(req.query.productTypes) ? req.query.productTypes :
                req.query.productTypes ? [req.query.productTypes] : [],
            // 营养成分筛选
            proteinMin: req.query.proteinMin ? parseFloat(req.query.proteinMin) : undefined,
            proteinMax: req.query.proteinMax ? parseFloat(req.query.proteinMax) : undefined,
            fatMin: req.query.fatMin ? parseFloat(req.query.fatMin) : undefined,
            fatMax: req.query.fatMax ? parseFloat(req.query.fatMax) : undefined,
            carbsMin: req.query.carbsMin ? parseFloat(req.query.carbsMin) : undefined,
            carbsMax: req.query.carbsMax ? parseFloat(req.query.carbsMax) : undefined,
            caloriesMin: req.query.caloriesMin ? parseFloat(req.query.caloriesMin) : undefined,
            caloriesMax: req.query.caloriesMax ? parseFloat(req.query.caloriesMax) : undefined,
            // 成分质量筛选
            minQualityIngredients: req.query.minQualityIngredients ? parseInt(req.query.minQualityIngredients) : undefined,
            maxQuestionableIngredients: req.query.maxQuestionableIngredients ? parseInt(req.query.maxQuestionableIngredients) : undefined,
            excludeAllergens: Array.isArray(req.query.excludeAllergens) ? req.query.excludeAllergens :
                req.query.excludeAllergens ? [req.query.excludeAllergens] : [],
            // 评价筛选
            minRating: req.query.minRating ? parseFloat(req.query.minRating) : undefined,
            minReviewCount: req.query.minReviewCount ? parseInt(req.query.minReviewCount) : undefined,
        };
        const results = await search_service_js_1.searchService.searchProductsWithFilters(filters, page, limit, sortBy, sortOrder);
        res.json(response_config_js_1.default.success({
            products: results.products,
            totalCount: results.totalCount,
            totalPages: results.totalPages,
            currentPage: page
        }, "筛选产品成功"));
    }
    catch (error) {
        console.error('Filter products error:', error);
        res.status(500).json(response_config_js_1.default.error(error.message || '筛选产品失败', 500));
    }
}));
exports.default = router;
//# sourceMappingURL=search-routes.js.map