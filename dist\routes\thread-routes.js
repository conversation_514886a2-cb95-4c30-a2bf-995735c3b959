"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const thread_service_1 = __importDefault(require("../services/thread-service"));
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const response_data_1 = require("../utils/response-data");
const router = express_1.default.Router();
// Configure multer for thread image uploads
const storage = multer_1.default.diskStorage({
    destination: (_req, _file, cb) => {
        const uploadDir = path_1.default.join(process.cwd(), 'uploads/threads');
        // Create directory if it doesn't exist
        if (!fs_1.default.existsSync(uploadDir)) {
            fs_1.default.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        // Generate unique filename with timestamp
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path_1.default.extname(file.originalname);
        const filename = `thread-${uniqueSuffix}${ext}`;
        cb(null, filename);
    }
});
const upload = (0, multer_1.default)({
    storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
    },
    fileFilter: (_req, file, cb) => {
        const allowedTypes = /jpeg|jpg|png|gif|webp/;
        const extname = allowedTypes.test(path_1.default.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);
        if (mimetype && extname) {
            return cb(null, true);
        }
        else {
            cb(new Error('只允许上传图片文件 (jpeg, jpg, png, gif, webp)'));
        }
    }
});
// 简单的认证中间件示例（需要根据实际情况调整）
const requireAuth = (req, res, next) => {
    // 这里应该实现实际的JWT token验证逻辑
    // 暂时使用header中的user-id作为简单认证
    const userId = req.headers['user-id'];
    if (!userId) {
        res.status(401).json({
            success: false,
            message: '需要用户认证'
        });
        return;
    }
    req.user = { id: userId, username: 'user' };
    next();
};
// 可选认证中间件
const optionalAuth = (req, res, next) => {
    const userId = req.headers['user-id'];
    if (userId) {
        req.user = { id: userId, username: 'user' };
    }
    next();
};
// POST /api/threads - 创建新帖子或回复
router.post('/', requireAuth, async (req, res) => {
    try {
        const { title, content, images, parent_thread_id } = req.body;
        // 验证必填字段
        // 如果是主帖（没有parent_thread_id），标题是必需的
        if (!parent_thread_id && (!title || title.trim().length === 0)) {
            res.status(400).json({
                success: false,
                message: '帖子标题不能为空'
            });
            return;
        }
        if (!content || content.trim().length === 0) {
            res.status(400).json({
                success: false,
                message: '帖子内容不能为空'
            });
            return;
        }
        // 如果提供了标题，验证长度
        if (title && title.length > 100) {
            res.status(400).json({
                success: false,
                message: '帖子标题不能超过100字符'
            });
            return;
        }
        if (content.length > 1000) {
            res.status(400).json({
                success: false,
                message: '帖子内容不能超过1000字符'
            });
            return;
        }
        if (images && images.length > 9) {
            res.status(400).json({
                success: false,
                message: '最多只能上传9张图片'
            });
            return;
        }
        const thread = await thread_service_1.default.createThread(req.user.id, {
            title,
            content,
            images,
            parent_thread_id
        });
        res.status(201).json({
            success: true,
            data: thread
        });
    }
    catch (error) {
        console.error('Create thread error:', error);
        res.status(500).json({
            success: false,
            message: '创建帖子失败',
            error: error.message
        });
    }
});
// GET /api/threads - 获取广场帖子列表
router.get('/', optionalAuth, async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 10, 50); // 限制最大50条
        const sortBy = req.query.sort_by || 'latest';
        if (page < 1) {
            res.status(400).json({
                success: false,
                message: '页码必须大于0'
            });
            return;
        }
        if (!['latest', 'updated', 'hot', 'following'].includes(sortBy)) {
            res.status(400).json({
                success: false,
                message: '排序方式只能是 latest、updated、hot 或 following'
            });
            return;
        }
        let result;
        if (sortBy === 'following') {
            // 关注者动态需要用户认证
            const userId = req.headers['user-id'];
            if (!userId) {
                res.status(401).json({
                    success: false,
                    message: '获取关注动态需要用户认证'
                });
                return;
            }
            result = await thread_service_1.default.getFollowingThreads(userId, page, limit);
        }
        else {
            result = await thread_service_1.default.getPublicThreads(page, limit, sortBy);
        }
        res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        console.error('Get threads error:', error);
        res.status(500).json({
            success: false,
            message: '获取帖子列表失败',
            error: error.message
        });
    }
});
// 审核列表：待审核帖子（主帖与回复均可）
router.get('/moderation/pending', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 20, 100);
        const skip = (page - 1) * limit;
        const Thread = (await Promise.resolve().then(() => __importStar(require('../models/thread')))).default;
        const items = await Thread.find({ is_approved: false, status: { $ne: 'deleted' } })
            .sort({ created_at: -1 })
            .skip(skip)
            .limit(limit);
        const total = await Thread.countDocuments({ is_approved: false, status: { $ne: 'deleted' } });
        res.json({ success: true, data: { items, total, page, limit } });
    }
    catch (error) {
        console.error('Get pending threads error:', error);
        res.status(500).json({ success: false, message: '获取待审核帖子失败', error: error.message });
    }
});
// 审核通过
router.post('/:id/approve', async (req, res) => {
    try {
        const threadId = req.params.id;
        const approver = req.headers['x-admin-id'] || 'admin';
        const Thread = (await Promise.resolve().then(() => __importStar(require('../models/thread')))).default;
        const updated = await Thread.findOneAndUpdate({ thread_id: threadId }, { is_approved: true, approved_by: approver, approved_at: new Date(), reject_reason: null }, { new: true });
        if (!updated) {
            res.status(404).json({ success: false, message: '帖子不存在' });
            return;
        }
        res.json({ success: true, data: updated, message: '审核通过' });
    }
    catch (error) {
        console.error('Approve thread error:', error);
        res.status(500).json({ success: false, message: '审核失败', error: error.message });
    }
});
// 审核拒绝
router.post('/:id/reject', async (req, res) => {
    try {
        const threadId = req.params.id;
        const { reason } = req.body;
        const approver = req.headers['x-admin-id'] || 'admin';
        const Thread = (await Promise.resolve().then(() => __importStar(require('../models/thread')))).default;
        const updated = await Thread.findOneAndUpdate({ thread_id: threadId }, { is_approved: false, approved_by: approver, approved_at: null, reject_reason: reason || '不符合发布规范' }, { new: true });
        if (!updated) {
            res.status(404).json({ success: false, message: '帖子不存在' });
            return;
        }
        res.json({ success: true, data: updated, message: '已拒绝' });
    }
    catch (error) {
        console.error('Reject thread error:', error);
        res.status(500).json({ success: false, message: '审核失败', error: error.message });
    }
});
// 批量审核通过
router.post('/batch/approve', async (req, res) => {
    try {
        const { threadIds } = req.body;
        const approver = req.headers['x-admin-id'] || 'admin';
        if (!threadIds || !Array.isArray(threadIds) || threadIds.length === 0) {
            res.status(400).json({ success: false, message: '请提供有效的帖子ID列表' });
            return;
        }
        const Thread = (await Promise.resolve().then(() => __importStar(require('../models/thread')))).default;
        const result = await Thread.updateMany({ thread_id: { $in: threadIds } }, {
            is_approved: true,
            approved_by: approver,
            approved_at: new Date(),
            reject_reason: null
        });
        res.json({
            success: true,
            data: {
                modifiedCount: result.modifiedCount,
                matchedCount: result.matchedCount
            },
            message: `成功审核通过 ${result.modifiedCount} 个帖子`
        });
    }
    catch (error) {
        console.error('Batch approve threads error:', error);
        res.status(500).json({ success: false, message: '批量审核失败', error: error.message });
    }
});
// 批量审核拒绝
router.post('/batch/reject', async (req, res) => {
    try {
        const { threadIds, reason } = req.body;
        const approver = req.headers['x-admin-id'] || 'admin';
        if (!threadIds || !Array.isArray(threadIds) || threadIds.length === 0) {
            res.status(400).json({ success: false, message: '请提供有效的帖子ID列表' });
            return;
        }
        const Thread = (await Promise.resolve().then(() => __importStar(require('../models/thread')))).default;
        const result = await Thread.updateMany({ thread_id: { $in: threadIds } }, {
            is_approved: false,
            approved_by: approver,
            approved_at: null,
            reject_reason: reason || '不符合发布规范'
        });
        res.json({
            success: true,
            data: {
                modifiedCount: result.modifiedCount,
                matchedCount: result.matchedCount
            },
            message: `成功拒绝 ${result.modifiedCount} 个帖子`
        });
    }
    catch (error) {
        console.error('Batch reject threads error:', error);
        res.status(500).json({ success: false, message: '批量拒绝失败', error: error.message });
    }
});
// 批量删除帖子
router.post('/batch/delete', async (req, res) => {
    try {
        const { threadIds } = req.body;
        if (!threadIds || !Array.isArray(threadIds) || threadIds.length === 0) {
            res.status(400).json({ success: false, message: '请提供有效的帖子ID列表' });
            return;
        }
        const Thread = (await Promise.resolve().then(() => __importStar(require('../models/thread')))).default;
        const result = await Thread.updateMany({ thread_id: { $in: threadIds } }, { status: 'deleted' });
        res.json({
            success: true,
            data: {
                modifiedCount: result.modifiedCount,
                matchedCount: result.matchedCount
            },
            message: `成功删除 ${result.modifiedCount} 个帖子`
        });
    }
    catch (error) {
        console.error('Batch delete threads error:', error);
        res.status(500).json({ success: false, message: '批量删除失败', error: error.message });
    }
});
// GET /api/threads/:id - 获取单个帖子详情和评论
router.get('/:id', optionalAuth, async (req, res) => {
    try {
        const threadId = req.params.id;
        const commentsPage = parseInt(req.query.comments_page) || 1;
        const commentsLimit = Math.min(parseInt(req.query.comments_limit) || 10, 50);
        if (commentsPage < 1) {
            res.status(400).json({
                success: false,
                message: '评论页码必须大于0'
            });
            return;
        }
        const result = await thread_service_1.default.getThreadDetail(threadId, commentsPage, commentsLimit);
        if (!result) {
            res.status(404).json({
                success: false,
                message: '帖子不存在'
            });
            return;
        }
        res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        console.error('Get thread detail error:', error);
        res.status(500).json({
            success: false,
            message: '获取帖子详情失败',
            error: error.message
        });
    }
});
// PATCH /api/threads/:id - 更新帖子内容
router.patch('/:id', requireAuth, async (req, res) => {
    try {
        const threadId = req.params.id;
        const { title, content, images } = req.body;
        const updatedThread = await thread_service_1.default.updateThread(threadId, req.user.id, {
            title,
            content,
            images
        });
        if (!updatedThread) {
            res.status(404).json({
                success: false,
                message: '帖子不存在或无权限编辑'
            });
            return;
        }
        res.json({
            success: true,
            data: updatedThread,
            message: '帖子更新成功'
        });
    }
    catch (error) {
        console.error('Update thread error:', error);
        res.status(500).json({
            success: false,
            message: error.message || '更新帖子失败',
            error: error.message
        });
    }
});
// DELETE /api/threads/:id - 删除帖子
router.delete('/:id', requireAuth, async (req, res) => {
    try {
        const threadId = req.params.id;
        const success = await thread_service_1.default.deleteThread(threadId, req.user.id);
        if (!success) {
            res.status(404).json({
                success: false,
                message: '帖子不存在或无权限删除'
            });
            return;
        }
        res.json({
            success: true,
            message: '帖子已删除'
        });
    }
    catch (error) {
        console.error('Delete thread error:', error);
        res.status(500).json({
            success: false,
            message: '删除帖子失败',
            error: error.message
        });
    }
});
// POST /api/threads/:id/like - 点赞或取消点赞
router.post('/:id/like', requireAuth, async (req, res) => {
    try {
        const threadId = req.params.id;
        const result = await thread_service_1.default.toggleLike(threadId, req.user.id);
        if (!result) {
            res.status(404).json({
                success: false,
                message: '帖子不存在'
            });
            return;
        }
        res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        console.error('Toggle like error:', error);
        res.status(500).json({
            success: false,
            message: '点赞操作失败',
            error: error.message
        });
    }
});
// POST /api/threads/:id/bookmark - 收藏或取消收藏
router.post('/:id/bookmark', requireAuth, async (req, res) => {
    try {
        const threadId = req.params.id;
        const result = await thread_service_1.default.toggleBookmark(threadId, req.user.id);
        if (!result) {
            res.status(404).json({
                success: false,
                message: '帖子不存在'
            });
            return;
        }
        res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        console.error('Toggle bookmark error:', error);
        res.status(500).json({
            success: false,
            message: '收藏操作失败',
            error: error.message
        });
    }
});
// GET /api/users/:userId/threads - 获取指定用户的帖子
router.get('/users/:userId/threads', optionalAuth, async (req, res) => {
    try {
        const userId = req.params.userId;
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 10, 50);
        if (page < 1) {
            res.status(400).json({
                success: false,
                message: '页码必须大于0'
            });
            return;
        }
        const result = await thread_service_1.default.getUserThreads(userId, page, limit);
        res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        console.error('Get user threads error:', error);
        res.status(500).json({
            success: false,
            message: '获取用户帖子失败',
            error: error.message
        });
    }
});
// GET /api/users/:userId/likes - 获取指定用户点赞的帖子
router.get('/users/:userId/likes', optionalAuth, async (req, res) => {
    try {
        const userId = req.params.userId;
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 10, 50);
        if (page < 1) {
            res.status(400).json({
                success: false,
                message: '页码必须大于0'
            });
            return;
        }
        const result = await thread_service_1.default.getUserLikedThreads(userId, page, limit);
        res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        console.error('Get user liked threads error:', error);
        res.status(500).json({
            success: false,
            message: '获取用户点赞帖子失败',
            error: error.message
        });
    }
});
// GET /api/users/:userId/bookmarks - 获取指定用户收藏的帖子
router.get('/users/:userId/bookmarks', requireAuth, async (req, res) => {
    try {
        const userId = req.params.userId;
        // 只允许用户查看自己的收藏
        if (userId !== req.user.id) {
            res.status(403).json({
                success: false,
                message: '只能查看自己的收藏'
            });
            return;
        }
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 10, 50);
        if (page < 1) {
            res.status(400).json({
                success: false,
                message: '页码必须大于0'
            });
            return;
        }
        const result = await thread_service_1.default.getUserBookmarkedThreads(userId, page, limit);
        res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        console.error('Get user bookmarked threads error:', error);
        res.status(500).json({
            success: false,
            message: '获取用户收藏帖子失败',
            error: error.message
        });
    }
});
// GET /api/threads/users/:userId/comments - 获取指定用户的评论（回复）
router.get('/users/:userId/comments', optionalAuth, async (req, res) => {
    try {
        const userId = req.params.userId;
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 10, 50);
        if (page < 1) {
            res.status(400).json({
                success: false,
                message: '页码必须大于0'
            });
            return;
        }
        const result = await thread_service_1.default.getUserComments(userId, page, limit);
        res.json({
            success: true,
            data: result
        });
    }
    catch (error) {
        console.error('Get user comments error:', error);
        res.status(500).json({
            success: false,
            message: '获取用户评论失败',
            error: error.message
        });
    }
});
// POST /api/threads/anonymous - 管理员匿名发帖
router.post('/anonymous', async (req, res) => {
    try {
        const { title, content, images } = req.body;
        console.log('Anonymous thread creation request:', { title, content, images }); // 调试日志
        // 验证必填字段
        if (!title || title.trim().length === 0) {
            res.status(400).json({
                success: false,
                message: '帖子标题不能为空'
            });
            return;
        }
        if (!content || content.trim().length === 0) {
            res.status(400).json({
                success: false,
                message: '帖子内容不能为空'
            });
            return;
        }
        if (title.length > 100) {
            res.status(400).json({
                success: false,
                message: '帖子标题不能超过100字符'
            });
            return;
        }
        if (content.length > 1000) {
            res.status(400).json({
                success: false,
                message: '帖子内容不能超过1000字符'
            });
            return;
        }
        if (images && images.length > 9) {
            res.status(400).json({
                success: false,
                message: '最多只能上传9张图片'
            });
            return;
        }
        // 使用匿名用户ID发帖
        const anonymousUserId = 'anonymous-user';
        const thread = await thread_service_1.default.createThread(anonymousUserId, {
            title,
            content,
            images,
            parent_thread_id: undefined // 匿名发帖不能是回复
        });
        res.status(201).json({
            success: true,
            data: thread
        });
    }
    catch (error) {
        console.error('Create anonymous thread error:', error);
        res.status(500).json({
            success: false,
            message: '创建帖子失败',
            error: error.message
        });
    }
});
// POST /api/threads/upload-image - 上传帖子图片
router.post('/upload-image', upload.single('image'), ((req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json(response_data_1.ResponseData.error("没有上传文件"));
        }
        // 返回相对路径而不是完整URL，让前端通过环境变量拼接
        const relativePath = `uploads/threads/${req.file.filename}`;
        res.json(response_data_1.ResponseData.success({
            imageUrl: relativePath,
            filename: req.file.filename,
        }));
    }
    catch (error) {
        console.error('Thread image upload error:', error);
        res.status(500).json(response_data_1.ResponseData.error(error.message || "图片上传失败"));
    }
}));
exports.default = router;
//# sourceMappingURL=thread-routes.js.map