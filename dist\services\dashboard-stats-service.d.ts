export interface DashboardOverviewStats {
    totalUsers: number;
    totalFontUsers: number;
    totalProducts: number;
    totalBrands: number;
    totalThreads: number;
    totalReviews: number;
    totalMessages: number;
}
export interface DashboardUserStats {
    newFontUsersToday: number;
    userGrowthRate: number;
    activeUsersToday: number;
}
export interface DashboardContentStats {
    newContentToday: {
        threads: number;
        reviews: number;
    };
    contentGrowthRate: number;
    topBrands: Array<{
        id: string;
        name: string;
        productCount: number;
        reviewCount: number;
        averageRating: number;
    }>;
}
export interface DashboardEngagementStats {
    totalLikes: number;
    totalBookmarks: number;
    totalFollows: number;
    engagementRate: number;
    newEngagementToday: {
        likes: number;
        bookmarks: number;
        follows: number;
    };
}
export interface DashboardTrendStats {
    userTrend: Array<{
        date: string;
        count: number;
    }>;
    contentTrend: Array<{
        date: string;
        threads: number;
        reviews: number;
    }>;
    engagementTrend: Array<{
        date: string;
        likes: number;
        bookmarks: number;
    }>;
}
export interface DashboardPopularStats {
    topBrands: Array<{
        id: string;
        name: string;
        productCount: number;
        reviewCount: number;
        averageRating: number;
    }>;
    topProducts: Array<{
        id: string;
        name: string;
        brand: string;
        reviewCount: number;
        averageRating: number;
    }>;
    topThreads: Array<{
        id: string;
        title: string;
        author: string;
        likes: number;
        replies: number;
    }>;
}
declare class DashboardStatsService {
    getOverviewStats(): Promise<DashboardOverviewStats>;
    getUserStats(): Promise<DashboardUserStats>;
    getContentStats(): Promise<DashboardContentStats>;
    getEngagementStats(): Promise<DashboardEngagementStats>;
    getTrendStats(days?: number): Promise<DashboardTrendStats>;
    getPopularStats(): Promise<DashboardPopularStats>;
    private getTopBrands;
    private getTopProducts;
    private getTopThreads;
    private getUserTrend;
    private getContentTrend;
    private getEngagementTrend;
}
declare const _default: DashboardStatsService;
export default _default;
//# sourceMappingURL=dashboard-stats-service.d.ts.map