"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const brand_repo_1 = __importDefault(require("../repository/brand-repo"));
const product_repo_1 = __importDefault(require("../repository/product-repo"));
const thread_repo_1 = __importDefault(require("../repository/thread-repo"));
const review_repo_1 = __importDefault(require("../repository/review-repo"));
const font_user_repo_1 = __importDefault(require("../repository/font-user-repo"));
const message_repo_1 = __importDefault(require("../repository/message-repo"));
const follow_repo_1 = __importDefault(require("../repository/follow-repo"));
class DashboardStatsService {
    // 获取概览统计
    async getOverviewStats() {
        try {
            const [totalUsers, totalFontUsers, totalProducts, totalBrands, totalThreads, totalReviews, totalMessages] = await Promise.all([
                font_user_repo_1.default.countAll(), // 管理员用户数量很少，主要统计前端用户
                font_user_repo_1.default.countAll(),
                product_repo_1.default.countAll(),
                brand_repo_1.default.countAll(),
                thread_repo_1.default.countAll(),
                review_repo_1.default.countAll(),
                message_repo_1.default.countAll()
            ]);
            return {
                totalUsers,
                totalFontUsers,
                totalProducts,
                totalBrands,
                totalThreads,
                totalReviews,
                totalMessages
            };
        }
        catch (error) {
            console.error('Error getting overview stats:', error);
            throw error;
        }
    }
    // 获取用户统计
    async getUserStats() {
        try {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            const [newFontUsersToday, totalUsersToday, totalUsersYesterday] = await Promise.all([
                font_user_repo_1.default.countByDateRange(today, new Date()),
                font_user_repo_1.default.countAll(),
                font_user_repo_1.default.countByDateRange(new Date(0), yesterday)
            ]);
            const userGrowthRate = totalUsersYesterday > 0
                ? ((totalUsersToday - totalUsersYesterday) / totalUsersYesterday) * 100
                : 0;
            return {
                newFontUsersToday,
                userGrowthRate,
                activeUsersToday: newFontUsersToday // 简化处理，实际应该统计活跃用户
            };
        }
        catch (error) {
            console.error('Error getting user stats:', error);
            throw error;
        }
    }
    // 获取内容统计
    async getContentStats() {
        try {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const [newThreadsToday, newReviewsToday, topBrands] = await Promise.all([
                thread_repo_1.default.countByDateRange(today, new Date()),
                review_repo_1.default.countByDateRange(today, new Date()),
                this.getTopBrands(5)
            ]);
            // 计算内容增长率（简化处理）
            const totalContentToday = newThreadsToday + newReviewsToday;
            const contentGrowthRate = totalContentToday > 0 ? 15.5 : 0; // 模拟增长率
            return {
                newContentToday: {
                    threads: newThreadsToday,
                    reviews: newReviewsToday
                },
                contentGrowthRate,
                topBrands
            };
        }
        catch (error) {
            console.error('Error getting content stats:', error);
            throw error;
        }
    }
    // 获取互动统计
    async getEngagementStats() {
        try {
            const [totalLikes, totalBookmarks, totalFollows] = await Promise.all([
                thread_repo_1.default.getTotalLikes(),
                thread_repo_1.default.getTotalBookmarks(),
                follow_repo_1.default.getTotalFollows()
            ]);
            const totalEngagement = totalLikes + totalBookmarks + totalFollows;
            const totalContent = await thread_repo_1.default.countAll();
            const engagementRate = totalContent > 0 ? (totalEngagement / totalContent) * 100 : 0;
            return {
                totalLikes,
                totalBookmarks,
                totalFollows,
                engagementRate,
                newEngagementToday: {
                    likes: Math.floor(totalLikes * 0.05), // 模拟今日新增
                    bookmarks: Math.floor(totalBookmarks * 0.03),
                    follows: Math.floor(totalFollows * 0.02)
                }
            };
        }
        catch (error) {
            console.error('Error getting engagement stats:', error);
            throw error;
        }
    }
    // 获取趋势数据
    async getTrendStats(days = 7) {
        try {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - days);
            const [userTrend, contentTrend, engagementTrend] = await Promise.all([
                this.getUserTrend(startDate, endDate),
                this.getContentTrend(startDate, endDate),
                this.getEngagementTrend(startDate, endDate)
            ]);
            return {
                userTrend,
                contentTrend,
                engagementTrend
            };
        }
        catch (error) {
            console.error('Error getting trend stats:', error);
            throw error;
        }
    }
    // 获取热门数据
    async getPopularStats() {
        try {
            const [topBrands, topProducts, topThreads] = await Promise.all([
                this.getTopBrands(5),
                this.getTopProducts(5),
                this.getTopThreads(5)
            ]);
            return {
                topBrands,
                topProducts,
                topThreads
            };
        }
        catch (error) {
            console.error('Error getting popular stats:', error);
            throw error;
        }
    }
    // 辅助方法：获取热门品牌
    async getTopBrands(limit) {
        try {
            const brands = await brand_repo_1.default.getTopBrands(limit);
            return brands.map(brand => ({
                id: brand._id.toString(),
                name: brand.name,
                productCount: brand.productCount || 0,
                reviewCount: brand.reviewCount || 0,
                averageRating: brand.averageRating || 0
            }));
        }
        catch (error) {
            console.error('Error getting top brands:', error);
            return [];
        }
    }
    // 辅助方法：获取热门产品
    async getTopProducts(limit) {
        try {
            const products = await product_repo_1.default.getTopProducts(limit);
            return products.map(product => ({
                id: product._id.toString(),
                name: product.name,
                brand: typeof product.brand === 'string' ? product.brand : product.brand?.name || '未知品牌',
                reviewCount: product.reviewCount || 0,
                averageRating: product.averageRating || 0
            }));
        }
        catch (error) {
            console.error('Error getting top products:', error);
            return [];
        }
    }
    // 辅助方法：获取热门帖子
    async getTopThreads(limit) {
        try {
            const threads = await thread_repo_1.default.getTopThreads(limit);
            return threads.map(thread => ({
                id: thread._id.toString(),
                title: thread.title,
                author: thread.user?.username || '匿名用户',
                likes: thread.likes?.length || 0,
                replies: thread.replies?.length || 0
            }));
        }
        catch (error) {
            console.error('Error getting top threads:', error);
            return [];
        }
    }
    // 辅助方法：获取用户趋势
    async getUserTrend(startDate, endDate) {
        try {
            const trend = await font_user_repo_1.default.getTrendData(startDate, endDate);
            return trend;
        }
        catch (error) {
            console.error('Error getting user trend:', error);
            return [];
        }
    }
    // 辅助方法：获取内容趋势
    async getContentTrend(startDate, endDate) {
        try {
            const [threadTrend, reviewTrend] = await Promise.all([
                thread_repo_1.default.getTrendData(startDate, endDate),
                review_repo_1.default.getTrendData(startDate, endDate)
            ]);
            // 合并趋势数据
            const combinedTrend = [];
            const dateMap = new Map();
            // 处理帖子趋势
            threadTrend.forEach(item => {
                const dateKey = item.date;
                if (!dateMap.has(dateKey)) {
                    dateMap.set(dateKey, { date: dateKey, threads: 0, reviews: 0 });
                }
                dateMap.get(dateKey).threads = item.count;
            });
            // 处理评论趋势
            reviewTrend.forEach(item => {
                const dateKey = item.date;
                if (!dateMap.has(dateKey)) {
                    dateMap.set(dateKey, { date: dateKey, threads: 0, reviews: 0 });
                }
                dateMap.get(dateKey).reviews = item.count;
            });
            return Array.from(dateMap.values()).sort((a, b) => a.date.localeCompare(b.date));
        }
        catch (error) {
            console.error('Error getting content trend:', error);
            return [];
        }
    }
    // 辅助方法：获取互动趋势
    async getEngagementTrend(startDate, endDate) {
        try {
            // 简化处理，返回模拟数据
            const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
            const trend = [];
            for (let i = 0; i < days; i++) {
                const date = new Date(startDate);
                date.setDate(date.getDate() + i);
                trend.push({
                    date: date.toISOString().split('T')[0],
                    likes: Math.floor(Math.random() * 50) + 10,
                    bookmarks: Math.floor(Math.random() * 30) + 5
                });
            }
            return trend;
        }
        catch (error) {
            console.error('Error getting engagement trend:', error);
            return [];
        }
    }
}
exports.default = new DashboardStatsService();
//# sourceMappingURL=dashboard-stats-service.js.map