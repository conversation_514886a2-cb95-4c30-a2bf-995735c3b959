"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const resend_1 = require("resend");
const app_config_1 = __importDefault(require("../config/app-config"));
class EmailService {
    resend;
    constructor() {
        this.resend = new resend_1.Resend(app_config_1.default.email.resendApiKey);
    }
    /**
     * 发送邮箱验证码
     * @param email 收件人邮箱
     * @param verificationCode 验证码
     * @param username 用户名
     */
    async sendVerificationEmail(email, verificationCode, username) {
        try {
            const result = await this.resend.emails.send({
                from: `${app_config_1.default.email.fromName} <${app_config_1.default.email.fromEmail}>`,
                to: [email],
                subject: '邮箱验证 - 好宠粮',
                html: this.generateVerificationEmailTemplate(verificationCode, username)
            });
            console.log('邮件发送成功:', result);
            return true;
        }
        catch (error) {
            console.error('邮件发送失败:', error);
            return false;
        }
    }
    /**
     * 发送密码重置邮件
     * @param email 收件人邮箱
     * @param resetToken 重置令牌
     * @param username 用户名
     */
    async sendPasswordResetEmail(email, resetToken, username) {
        try {
            const result = await this.resend.emails.send({
                from: `${app_config_1.default.email.fromName} <${app_config_1.default.email.fromEmail}>`,
                to: [email],
                subject: '密码重置 - 好宠粮',
                html: this.generatePasswordResetEmailTemplate(resetToken, username)
            });
            console.log('密码重置邮件发送成功:', result);
            return true;
        }
        catch (error) {
            console.error('密码重置邮件发送失败:', error);
            return false;
        }
    }
    /**
     * 生成邮箱验证邮件模板
     */
    generateVerificationEmailTemplate(verificationCode, username) {
        return `
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>邮箱验证</title>
            <style>
                body {
                    font-family: 'Helvetica Neue', Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f4f4f4;
                }
                .container {
                    background-color: #ffffff;
                    padding: 40px;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                }
                .logo {
                    font-size: 28px;
                    font-weight: bold;
                    color: #006400;
                    margin-bottom: 10px;
                }
                .verification-code {
                    background-color: #f8f9fa;
                    border: 2px dashed #006400;
                    padding: 20px;
                    text-align: center;
                    margin: 30px 0;
                    border-radius: 8px;
                }
                .code {
                    font-size: 32px;
                    font-weight: bold;
                    color: #ff6b35;
                    letter-spacing: 5px;
                    font-family: 'Courier New', monospace;
                }
                .footer {
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #eee;
                    font-size: 14px;
                    color: #666;
                    text-align: center;
                }
                .warning {
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    color: #856404;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">好宠粮</div>
                    <h1>邮箱验证</h1>
                </div>
                
                <p>亲爱的 <strong>${username}</strong>，</p>
                
                <p>感谢您注册好宠粮！为了确保您的账户安全，请使用以下验证码完成邮箱验证：</p>
                
                <div class="verification-code">
                    <div class="code">${verificationCode}</div>
                </div>
                
                <div class="warning">
                    <strong>重要提醒：</strong>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>验证码有效期为 <strong>10分钟</strong></li>
                        <li>请勿将验证码泄露给他人</li>
                        <li>如果您没有注册好宠粮账户，请忽略此邮件</li>
                    </ul>
                </div>
                
                <p>如果您在验证过程中遇到任何问题，请联系我们的客服团队。</p>
                
                <div class="footer">
                    <p>此邮件由系统自动发送，请勿回复。</p>
                    <p>&copy; 2025 好宠粮. 保留所有权利。</p>
                </div>
            </div>
        </body>
        </html>
        `;
    }
    /**
     * 生成密码重置邮件模板
     */
    generatePasswordResetEmailTemplate(resetToken, username) {
        return `
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>密码重置</title>
            <style>
                body {
                    font-family: 'Helvetica Neue', Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f4f4f4;
                }
                .container {
                    background-color: #ffffff;
                    padding: 40px;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                }
                .logo {
                    font-size: 28px;
                    font-weight: bold;
                    color: #006400;
                    margin-bottom: 10px;
                }
                .reset-button {
                    display: inline-block;
                    background-color: #006400;
                    color: white;
                    padding: 15px 30px;
                    text-decoration: none;
                    border-radius: 5px;
                    font-weight: bold;
                    margin: 20px 0;
                }
                .footer {
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #eee;
                    font-size: 14px;
                    color: #666;
                    text-align: center;
                }
                .warning {
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    color: #856404;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">🐾 好宠粮</div>
                    <h1>密码重置</h1>
                </div>
                
                <p>亲爱的 <strong>${username}</strong>，</p>
                
                <p>我们收到了您的密码重置请求。如果这是您本人的操作，请点击下面的按钮重置密码：</p>
                
                <div style="text-align: center;">
                    <a href="#" class="reset-button">重置密码</a>
                </div>
                
                <div class="warning">
                    <strong>安全提醒：</strong>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>重置链接有效期为 <strong>30分钟</strong></li>
                        <li>如果您没有请求重置密码，请忽略此邮件</li>
                        <li>为了您的账户安全，请设置强密码</li>
                    </ul>
                </div>
                
                <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
                <p style="word-break: break-all; background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
                    重置令牌：${resetToken}
                </p>
                
                <div class="footer">
                    <p>此邮件由系统自动发送，请勿回复。</p>
                    <p>&copy; 2025 好宠粮. 保留所有权利。</p>
                </div>
            </div>
        </body>
        </html>
        `;
    }
}
exports.default = new EmailService();
//# sourceMappingURL=email-service.js.map