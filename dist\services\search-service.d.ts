export interface SearchFilters {
    category?: string;
    sortBy?: string;
}
export interface SearchPreviewResult {
    products: any[];
    brands: any[];
}
export interface SearchDetailedResult {
    products: any[];
    brands: any[];
    threads: any[];
    totalCount: number;
    totalPages: number;
}
declare class SearchService {
    /**
     * 实时搜索预览 - 返回少量结果用于快速预览
     */
    searchPreview(query: string): Promise<SearchPreviewResult>;
    /**
     * 详细搜索结果 - 支持分页和过滤
     */
    searchDetailed(query: string, page?: number, limit?: number): Promise<SearchDetailedResult>;
    /**
     * 搜索帖子
     */
    private searchThreads;
    /**
     * 获取热门搜索词
     */
    getHotSearches(): Promise<string[]>;
    /**
     * 获取搜索建议
     */
    getSearchSuggestions(query: string): Promise<string[]>;
    /**
     * 搜索产品 - 支持分页
     */
    searchProducts(query: string, page?: number, limit?: number): Promise<{
        products: {
            _id: string;
            id: string;
            name: string;
            brand: string;
            image_url: string | undefined;
            product_type: string | undefined;
            category: string | undefined;
            rating: number;
            review_count: number;
        }[];
        totalCount: number;
        totalPages: number;
    }>;
    /**
     * 搜索品牌 - 支持分页
     */
    searchBrands(query: string, page?: number, limit?: number): Promise<{
        brands: {
            _id: string | undefined;
            id: string | undefined;
            name: string;
            image_url: string | null | undefined;
            image: string | null | undefined;
            product_count: number;
            products: number;
            rating: number;
            review_count: number;
        }[];
        totalCount: number;
        totalPages: number;
    }>;
    /**
     * 搜索帖子 - 支持分页
     */
    searchThreadsWithPagination(query: string, page?: number, limit?: number): Promise<{
        threads: {
            thread_id: string;
            title: string;
            content: string;
            user_id: string;
            like_count: number;
            reply_count: number;
            created_at: Date;
            user: {
                id: string;
                username: string;
                avatar: string | undefined;
            };
        }[];
        totalCount: number;
        totalPages: number;
    }>;
    /**
     * 计算搜索帖子的总数
     */
    private countSearchThreads;
    /**
     * 高级产品筛选 - 支持多维度筛选
     */
    searchProductsWithFilters(filters: any, page?: number, limit?: number, sortBy?: string, sortOrder?: string): Promise<{
        products: {
            _id: any;
            id: any;
            name: any;
            brand: any;
            image_url: any;
            product_type: any;
            category: any;
            rating: number;
            review_count: number;
            protein: any;
            fat: any;
            carbs: any;
            calories: any;
            quality_ingredients_count: any;
            questionable_ingredients_count: any;
            quality_score: any;
        }[];
        totalCount: any;
        totalPages: number;
    }>;
}
export declare const searchService: SearchService;
export {};
//# sourceMappingURL=search-service.d.ts.map