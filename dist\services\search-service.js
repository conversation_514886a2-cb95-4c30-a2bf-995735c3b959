"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.searchService = void 0;
const product_repo_js_1 = __importDefault(require("../repository/product-repo.js"));
const brand_repo_js_1 = __importDefault(require("../repository/brand-repo.js"));
const thread_repo_js_1 = __importDefault(require("../repository/thread-repo.js"));
const font_user_repo_js_1 = __importDefault(require("../repository/font-user-repo.js"));
const product_review_summary_service_js_1 = __importDefault(require("./product-review-summary-service.js"));
const brand_review_summary_service_js_1 = __importDefault(require("./brand-review-summary-service.js"));
class SearchService {
    /**
     * 实时搜索预览 - 返回少量结果用于快速预览
     */
    async searchPreview(query) {
        try {
            // 搜索产品 - 限制5个结果
            const products = await product_repo_js_1.default.findByNameFuzzy(query, 0, 8);
            // 搜索品牌 - 限制3个结果
            const brands = await brand_repo_js_1.default.findRaw({
                name: { $regex: query, $options: 'i' }
            });
            const limitedBrands = brands.slice(0, 5);
            return {
                products: products.map(product => ({
                    _id: product._id,
                    id: product._id,
                    name: product.name,
                })),
                brands: limitedBrands.map(brand => ({
                    _id: brand._id,
                    id: brand._id, // 同时提供两个字段
                    name: brand.name,
                }))
            };
        }
        catch (error) {
            console.error('Search preview service error:', error);
            throw error;
        }
    }
    /**
     * 详细搜索结果 - 支持分页和过滤
     */
    async searchDetailed(query, page = 1, limit = 20) {
        try {
            const skip = (page - 1) * limit;
            // 构建产品搜索条件
            let productQuery = {
                $or: [
                    { name: { $regex: query, $options: 'i' } },
                    { brand: { $regex: query, $options: 'i' } },
                    { product_type: { $regex: query, $options: 'i' } }
                ]
            };
            // 搜索产品
            const products = await product_repo_js_1.default.findRaw(productQuery, skip, limit);
            const totalProductCount = await product_repo_js_1.default.countRaw(productQuery);
            // 为每个产品获取真实的评分和评论数据
            const productsWithReviewData = await Promise.all(products.map(async (product) => {
                const reviewSummary = await product_review_summary_service_js_1.default.getReviewSummaryByProductId(product._id.toString());
                return {
                    _id: product._id,
                    id: product._id,
                    name: product.name,
                    brand: product.brand,
                    image_url: product.image_url,
                    product_type: product.product_type,
                    category: product.product_type,
                    rating: reviewSummary?.average_rating || 0,
                    review_count: reviewSummary?.review_count || 0
                };
            }));
            // 搜索品牌
            const brands = await brand_repo_js_1.default.findRaw({
                name: { $regex: query, $options: 'i' }
            });
            // 搜索帖子
            const threads = await this.searchThreads(query, 0, 10); // 限制帖子数量
            // 计算总数和总页数
            const totalCount = totalProductCount + brands.length + threads.length;
            const totalPages = Math.ceil(totalProductCount / limit); // 主要基于产品分页
            return {
                products: productsWithReviewData,
                brands: brands.slice(0, 5).map(brand => ({
                    _id: brand._id,
                    id: brand._id, // 同时提供两个字段
                    name: brand.name,
                    image_url: brand.logo_url,
                    image: brand.logo_url,
                    product_count: Math.floor(Math.random() * 100) + 10,
                    products: Math.floor(Math.random() * 100) + 10 // 同时提供两个字段
                })),
                threads: threads,
                totalCount,
                totalPages
            };
        }
        catch (error) {
            console.error('Search detailed service error:', error);
            throw error;
        }
    }
    /**
     * 搜索帖子
     */
    async searchThreads(query, skip = 0, limit = 10) {
        try {
            const threadQuery = {
                $and: [
                    { status: 'visible' },
                    { reply_level: 0 }, // 只搜索主帖
                    {
                        $or: [
                            { title: { $regex: query, $options: 'i' } },
                            { content: { $regex: query, $options: 'i' } }
                        ]
                    }
                ]
            };
            const threads = await thread_repo_js_1.default.findRaw(threadQuery, skip, limit);
            // 获取用户信息
            const threadsWithUserInfo = [];
            for (const thread of threads) {
                const user = await font_user_repo_js_1.default.findById(thread.user_id);
                if (user) {
                    threadsWithUserInfo.push({
                        thread_id: thread.thread_id,
                        title: thread.title,
                        content: thread.content,
                        user_id: thread.user_id,
                        like_count: thread.like_count,
                        reply_count: thread.reply_count,
                        created_at: thread.created_at,
                        user: {
                            id: user.id,
                            username: user.username,
                            avatar: user.avatar
                        }
                    });
                }
            }
            return threadsWithUserInfo;
        }
        catch (error) {
            console.error('Search threads error:', error);
            return [];
        }
    }
    /**
     * 获取热门搜索词
     */
    async getHotSearches() {
        try {
            // 这里可以从数据库获取真实的热门搜索词
            // 暂时返回固定的热门搜索词
            return [
                '天然粮',
                '幼猫粮',
                '减肥粮',
                '无谷粮',
                '皇家',
                '希尔斯',
                '冠能',
                '渴望',
                '处方粮',
                '幼犬粮'
            ];
        }
        catch (error) {
            console.error('Get hot searches error:', error);
            throw error;
        }
    }
    /**
     * 获取搜索建议
     */
    async getSearchSuggestions(query) {
        try {
            // 从品牌和产品名称中获取建议
            const brandSuggestions = await brand_repo_js_1.default.findRaw({
                name: { $regex: `^${query}`, $options: 'i' }
            });
            const productSuggestions = await product_repo_js_1.default.findByNameFuzzy(query, 0, 5);
            const suggestions = [
                ...brandSuggestions.map(brand => brand.name),
                ...productSuggestions.map(product => product.name)
            ];
            // 去重并限制数量
            return [...new Set(suggestions)].slice(0, 8);
        }
        catch (error) {
            console.error('Get search suggestions error:', error);
            return [];
        }
    }
    /**
     * 搜索产品 - 支持分页
     */
    async searchProducts(query, page = 1, limit = 20) {
        try {
            const skip = (page - 1) * limit;
            // 构建产品搜索条件
            const productQuery = {
                $or: [
                    { name: { $regex: query, $options: 'i' } },
                    { brand: { $regex: query, $options: 'i' } },
                    { product_type: { $regex: query, $options: 'i' } }
                ]
            };
            // 搜索产品
            const products = await product_repo_js_1.default.findRaw(productQuery, skip, limit);
            const totalCount = await product_repo_js_1.default.countRaw(productQuery);
            // 为每个产品获取真实的评分和评论数据
            const productsWithReviewData = await Promise.all(products.map(async (product) => {
                const reviewSummary = await product_review_summary_service_js_1.default.getReviewSummaryByProductId(product._id.toString());
                return {
                    _id: product._id,
                    id: product._id,
                    name: product.name,
                    brand: product.brand,
                    image_url: product.image_url,
                    product_type: product.product_type,
                    category: product.product_type,
                    rating: reviewSummary?.average_rating || 0,
                    review_count: reviewSummary?.review_count || 0
                };
            }));
            const totalPages = Math.ceil(totalCount / limit);
            return {
                products: productsWithReviewData,
                totalCount,
                totalPages
            };
        }
        catch (error) {
            console.error('Search products service error:', error);
            throw error;
        }
    }
    /**
     * 搜索品牌 - 支持分页
     */
    async searchBrands(query, page = 1, limit = 20) {
        try {
            const skip = (page - 1) * limit;
            // 搜索品牌
            const allBrands = await brand_repo_js_1.default.findRaw({
                name: { $regex: query, $options: 'i' }
            });
            const totalCount = allBrands.length;
            const brands = allBrands.slice(skip, skip + limit);
            // 为每个品牌获取真实的评分和评论数据
            const brandsWithReviewData = await Promise.all(brands.map(async (brand) => {
                const brandId = brand._id?.toString() || '';
                const reviewSummary = await brand_review_summary_service_js_1.default.getReviewSummaryByBrandId(brandId);
                // 计算该品牌的产品数量
                const productCount = await product_repo_js_1.default.countRaw({ brand: brand.name });
                return {
                    _id: brand._id,
                    id: brand._id,
                    name: brand.name,
                    image_url: brand.logo_url,
                    image: brand.logo_url,
                    product_count: productCount,
                    products: productCount,
                    rating: reviewSummary?.average_rating || 0,
                    review_count: reviewSummary?.review_count || 0
                };
            }));
            const totalPages = Math.ceil(totalCount / limit);
            return {
                brands: brandsWithReviewData,
                totalCount,
                totalPages
            };
        }
        catch (error) {
            console.error('Search brands service error:', error);
            throw error;
        }
    }
    /**
     * 搜索帖子 - 支持分页
     */
    async searchThreadsWithPagination(query, page = 1, limit = 20) {
        try {
            const skip = (page - 1) * limit;
            // 搜索帖子
            const threads = await this.searchThreads(query, skip, limit);
            // 获取总数（这里需要一个单独的计数方法）
            const totalThreads = await this.countSearchThreads(query);
            const totalPages = Math.ceil(totalThreads / limit);
            return {
                threads,
                totalCount: totalThreads,
                totalPages
            };
        }
        catch (error) {
            console.error('Search threads with pagination service error:', error);
            throw error;
        }
    }
    /**
     * 计算搜索帖子的总数
     */
    async countSearchThreads(query) {
        try {
            // 构建与searchThreads相同的查询条件
            const threadQuery = {
                $and: [
                    {
                        $or: [
                            { title: { $regex: query, $options: 'i' } },
                            { content: { $regex: query, $options: 'i' } }
                        ]
                    },
                    { parent_thread_id: null }, // 只搜索主帖，不包含评论
                    { status: 'visible' }
                ]
            };
            return await thread_repo_js_1.default.countRaw(threadQuery);
        }
        catch (error) {
            console.error('Count search threads error:', error);
            return 0;
        }
    }
    /**
     * 高级产品筛选 - 支持多维度筛选
     */
    async searchProductsWithFilters(filters, page = 1, limit = 20, sortBy = 'quality', sortOrder = 'desc') {
        try {
            const skip = (page - 1) * limit;
            // 构建基础查询条件
            let query = {};
            // 搜索关键词
            if (filters.searchQuery && filters.searchQuery.trim()) {
                query.$or = [
                    { name: { $regex: filters.searchQuery, $options: 'i' } },
                    { brand: { $regex: filters.searchQuery, $options: 'i' } },
                    { product_type: { $regex: filters.searchQuery, $options: 'i' } }
                ];
            }
            // 品牌筛选
            if (filters.brands && filters.brands.length > 0) {
                query.brand = { $in: filters.brands };
            }
            // 产品类型筛选
            if (filters.productTypes && filters.productTypes.length > 0) {
                query.product_type = { $in: filters.productTypes };
            }
            // 过敏原筛选 - 排除包含指定过敏原的产品
            if (filters.excludeAllergens && filters.excludeAllergens.length > 0) {
                query.allergen_ingredients = { $nin: filters.excludeAllergens };
            }
            // 成分质量筛选
            if (filters.minQualityIngredients !== undefined) {
                query.$expr = query.$expr || { $and: [] };
                if (!Array.isArray(query.$expr.$and)) {
                    query.$expr.$and = [];
                }
                query.$expr.$and.push({
                    $gte: [{ $size: { $ifNull: ["$quality_ingredients", []] } }, filters.minQualityIngredients]
                });
            }
            if (filters.maxQuestionableIngredients !== undefined) {
                query.$expr = query.$expr || { $and: [] };
                if (!Array.isArray(query.$expr.$and)) {
                    query.$expr.$and = [];
                }
                query.$expr.$and.push({
                    $lte: [{ $size: { $ifNull: ["$questionable_ingredients", []] } }, filters.maxQuestionableIngredients]
                });
            }
            // 使用聚合管道进行复杂筛选和排序
            const pipeline = [
                { $match: query }
            ];
            // 添加计算字段
            pipeline.push({
                $addFields: {
                    qualityIngredientsCount: { $size: { $ifNull: ["$quality_ingredients", []] } },
                    questionableIngredientsCount: { $size: { $ifNull: ["$questionable_ingredients", []] } },
                    proteinValue: {
                        $toDouble: {
                            $replaceAll: {
                                input: { $ifNull: ["$dry_matter_analysis.protein", "0%"] },
                                find: "%",
                                replacement: ""
                            }
                        }
                    },
                    fatValue: {
                        $toDouble: {
                            $replaceAll: {
                                input: { $ifNull: ["$dry_matter_analysis.fat", "0%"] },
                                find: "%",
                                replacement: ""
                            }
                        }
                    },
                    carbsValue: {
                        $toDouble: {
                            $replaceAll: {
                                input: { $ifNull: ["$dry_matter_analysis.carbs", "0%"] },
                                find: "%",
                                replacement: ""
                            }
                        }
                    },
                    caloriesValue: {
                        $let: {
                            vars: {
                                match: {
                                    $regexFind: {
                                        input: { $ifNull: ["$est_calories", "0"] },
                                        regex: "\\d+\\.?\\d*"
                                    }
                                }
                            },
                            in: {
                                $toDouble: {
                                    $ifNull: ["$$match.match", "0"]
                                }
                            }
                        }
                    },
                    qualityScore: {
                        $subtract: [
                            { $add: [
                                    { $toDouble: {
                                            $replaceAll: {
                                                input: { $ifNull: ["$dry_matter_analysis.protein", "0%"] },
                                                find: "%",
                                                replacement: ""
                                            }
                                        } },
                                    { $size: { $ifNull: ["$quality_ingredients", []] } }
                                ] },
                            { $add: [
                                    { $toDouble: {
                                            $replaceAll: {
                                                input: { $ifNull: ["$dry_matter_analysis.carbs", "0%"] },
                                                find: "%",
                                                replacement: ""
                                            }
                                        } },
                                    { $size: { $ifNull: ["$questionable_ingredients", []] } }
                                ] }
                        ]
                    }
                }
            });
            // 营养成分范围筛选
            const nutritionFilters = {};
            if (filters.proteinMin !== undefined || filters.proteinMax !== undefined) {
                nutritionFilters.proteinValue = {};
                if (filters.proteinMin !== undefined)
                    nutritionFilters.proteinValue.$gte = filters.proteinMin;
                if (filters.proteinMax !== undefined)
                    nutritionFilters.proteinValue.$lte = filters.proteinMax;
            }
            if (filters.fatMin !== undefined || filters.fatMax !== undefined) {
                nutritionFilters.fatValue = {};
                if (filters.fatMin !== undefined)
                    nutritionFilters.fatValue.$gte = filters.fatMin;
                if (filters.fatMax !== undefined)
                    nutritionFilters.fatValue.$lte = filters.fatMax;
            }
            if (filters.carbsMin !== undefined || filters.carbsMax !== undefined) {
                nutritionFilters.carbsValue = {};
                if (filters.carbsMin !== undefined)
                    nutritionFilters.carbsValue.$gte = filters.carbsMin;
                if (filters.carbsMax !== undefined)
                    nutritionFilters.carbsValue.$lte = filters.carbsMax;
            }
            if (filters.caloriesMin !== undefined || filters.caloriesMax !== undefined) {
                nutritionFilters.caloriesValue = {};
                if (filters.caloriesMin !== undefined)
                    nutritionFilters.caloriesValue.$gte = filters.caloriesMin;
                if (filters.caloriesMax !== undefined)
                    nutritionFilters.caloriesValue.$lte = filters.caloriesMax;
            }
            if (Object.keys(nutritionFilters).length > 0) {
                pipeline.push({ $match: nutritionFilters });
            }
            // 排序
            let sortCriteria = {};
            switch (sortBy) {
                case 'protein':
                    sortCriteria = { proteinValue: sortOrder === 'desc' ? -1 : 1 };
                    break;
                case 'fat':
                    sortCriteria = { fatValue: sortOrder === 'desc' ? -1 : 1 };
                    break;
                case 'carbs':
                    sortCriteria = { carbsValue: sortOrder === 'desc' ? -1 : 1 };
                    break;
                case 'calories':
                    sortCriteria = { caloriesValue: sortOrder === 'desc' ? -1 : 1 };
                    break;
                case 'quality_ingredients':
                    sortCriteria = { qualityIngredientsCount: sortOrder === 'desc' ? -1 : 1 };
                    break;
                case 'questionable_ingredients':
                    sortCriteria = { questionableIngredientsCount: sortOrder === 'desc' ? -1 : 1 };
                    break;
                default:
                    // 默认按质量评分排序
                    sortCriteria = { qualityScore: sortOrder === 'desc' ? -1 : 1 };
            }
            pipeline.push({ $sort: sortCriteria });
            // 获取总数
            const countPipeline = [...pipeline, { $count: "total" }];
            const countResult = await product_repo_js_1.default.aggregate(countPipeline);
            const totalCount = countResult[0]?.total || 0;
            const totalPages = Math.ceil(totalCount / limit);
            // 分页
            pipeline.push({ $skip: skip });
            pipeline.push({ $limit: limit });
            // 执行查询
            const products = await product_repo_js_1.default.aggregate(pipeline);
            // 为每个产品获取评分和评论数据
            const productsWithReviewData = await Promise.all(products.map(async (product) => {
                const reviewSummary = await product_review_summary_service_js_1.default.getReviewSummaryByProductId(product._id.toString());
                return {
                    _id: product._id,
                    id: product._id,
                    name: product.name,
                    brand: product.brand,
                    image_url: product.image_url,
                    product_type: product.product_type,
                    category: product.product_type,
                    rating: reviewSummary?.average_rating || 0,
                    review_count: reviewSummary?.review_count || 0,
                    // 包含营养信息用于前端显示
                    protein: product.proteinValue,
                    fat: product.fatValue,
                    carbs: product.carbsValue,
                    calories: product.caloriesValue,
                    quality_ingredients_count: product.qualityIngredientsCount,
                    questionable_ingredients_count: product.questionableIngredientsCount,
                    quality_score: product.qualityScore
                };
            }));
            // 如果有评分筛选，在这里进行过滤
            let filteredProducts = productsWithReviewData;
            if (filters.minRating !== undefined) {
                filteredProducts = filteredProducts.filter(product => product.rating >= filters.minRating);
            }
            if (filters.minReviewCount !== undefined) {
                filteredProducts = filteredProducts.filter(product => product.review_count >= filters.minReviewCount);
            }
            return {
                products: filteredProducts,
                totalCount: filteredProducts.length < productsWithReviewData.length ? filteredProducts.length : totalCount,
                totalPages: Math.ceil(filteredProducts.length / limit),
            };
        }
        catch (error) {
            console.error('Search products with filters error:', error);
            throw error;
        }
    }
}
exports.searchService = new SearchService();
//# sourceMappingURL=search-service.js.map