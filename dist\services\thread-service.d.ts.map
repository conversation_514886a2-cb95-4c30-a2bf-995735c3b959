{"version": 3, "file": "thread-service.d.ts", "sourceRoot": "", "sources": ["../../src/services/thread-service.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,UAAU,EAAkB,mBAAmB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAGlI,cAAM,aAAa;IAEX,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,mBAAmB,GAAG,OAAO,CAAC,UAAU,CAAC;IAoE5E,gBAAgB,CAAC,IAAI,GAAE,MAAU,EAAE,KAAK,GAAE,MAAW,EAAE,MAAM,GAAE,QAAQ,GAAG,SAAS,GAAG,KAAgB,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAcpI,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,GAAE,MAAU,EAAE,aAAa,GAAE,MAAW,GAAG,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC;IAc7H,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE;QAAE,KAAK,CAAC,EAAE,MAAM,CAAC;QAAC,OAAO,CAAC,EAAE,MAAM,CAAC;QAAC,MAAM,CAAC,EAAE,MAAM,EAAE,CAAA;KAAE,GAAG,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;IAkDzI,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAqBhE,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC;QAAE,KAAK,EAAE,OAAO,CAAC;QAAC,UAAU,EAAE,MAAM,CAAA;KAAE,GAAG,IAAI,CAAC;IA6BpG,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC;QAAE,UAAU,EAAE,OAAO,CAAC;QAAC,cAAc,EAAE,MAAM,CAAA;KAAE,GAAG,IAAI,CAAC;IA6BjH,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,GAAE,MAAU,EAAE,KAAK,GAAE,MAAW,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAcjG,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,GAAE,MAAU,EAAE,KAAK,GAAE,MAAW,GAAG,OAAO,CAAC,kBAAkB,CAAC;IActG,wBAAwB,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,GAAE,MAAU,EAAE,KAAK,GAAE,MAAW,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAc3G,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,GAAE,MAAU,EAAE,KAAK,GAAE,MAAW,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAclG,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,GAAE,MAAU,EAAE,KAAK,GAAE,MAAW,GAAG,OAAO,CAAC,kBAAkB,CAAC;YAc9F,yBAAyB;YASzB,yBAAyB;YASzB,4BAA4B;YAS5B,4BAA4B;YAS5B,2BAA2B;YAS3B,2BAA2B;CAO1C;AAED,QAAA,MAAM,aAAa,eAAsB,CAAC;AAC1C,eAAe,aAAa,CAAC"}