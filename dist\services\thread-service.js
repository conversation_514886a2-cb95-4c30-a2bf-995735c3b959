"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const thread_repo_1 = __importDefault(require("../repository/thread-repo"));
const font_user_repo_1 = __importDefault(require("../repository/font-user-repo"));
const uuid_1 = require("uuid");
class ThreadService {
    // 创建新帖子或回复
    async createThread(userId, data) {
        const threadId = (0, uuid_1.v4)();
        let reply_level = 0;
        let root_thread_id = undefined;
        // 如果是回复，计算层级和根帖子ID
        if (data.parent_thread_id) {
            const parentThread = await thread_repo_1.default.findByThreadId(data.parent_thread_id);
            if (!parentThread) {
                throw new Error('Parent thread not found');
            }
            // 计算回复层级
            reply_level = Math.min(parentThread.reply_level + 1, 2); // 最多2级回复
            // 设置根帖子ID
            root_thread_id = parentThread.root_thread_id || parentThread.thread_id;
            // 检查是否超过回复层级限制
            if (parentThread.reply_level >= 2) {
                throw new Error('Maximum reply depth exceeded');
            }
        }
        const threadData = {
            thread_id: threadId,
            user_id: userId,
            title: data.title ? data.title.trim() : '', // 回复可以没有标题
            content: data.content.trim(),
            images: data.images || [],
            parent_thread_id: data.parent_thread_id || undefined,
            root_thread_id: root_thread_id,
            reply_to_user_id: data.reply_to_user_id || undefined,
            reply_level: reply_level,
            liked_by: [],
            bookmarked_by: [],
            like_count: 0,
            reply_count: 0,
            bookmark_count: 0,
            status: 'visible',
            is_approved: false,
            approved_by: null,
            approved_at: null,
            reject_reason: null,
            created_at: new Date(),
            updated_at: new Date()
        };
        // 创建帖子
        const createdThread = await thread_repo_1.default.create(threadData);
        // 更新用户发帖数
        if (!data.parent_thread_id) {
            // 只有主帖才计入用户发帖数
            await this.incrementUserThreadsCount(userId);
        }
        else {
            // 如果是回复，更新父帖子的回复数和根帖子的回复数
            await thread_repo_1.default.incrementReplyCount(data.parent_thread_id);
            if (root_thread_id && root_thread_id !== data.parent_thread_id) {
                await thread_repo_1.default.incrementReplyCount(root_thread_id);
            }
        }
        return createdThread;
    }
    // 获取广场帖子列表
    async getPublicThreads(page = 1, limit = 10, sortBy = 'latest') {
        const skip = (page - 1) * limit;
        const threads = await thread_repo_1.default.findPublicThreads(skip, limit, sortBy);
        const total = await thread_repo_1.default.countPublicThreads();
        return {
            threads,
            total,
            page,
            limit
        };
    }
    // 获取帖子详情和评论
    async getThreadDetail(threadId, commentsPage = 1, commentsLimit = 10) {
        const result = await thread_repo_1.default.findThreadWithComments(threadId, commentsPage, commentsLimit);
        if (!result.thread) {
            return null;
        }
        return {
            thread: result.thread,
            comments: result.comments
        };
    }
    // 更新帖子内容
    async updateThread(threadId, userId, data) {
        const thread = await thread_repo_1.default.findByIdWithUser(threadId);
        if (!thread || thread.user_id !== userId) {
            return null;
        }
        // 验证输入数据
        if (data.title !== undefined) {
            if (!data.title || data.title.trim().length === 0) {
                throw new Error('帖子标题不能为空');
            }
            if (data.title.length > 100) {
                throw new Error('帖子标题不能超过100字符');
            }
        }
        if (data.content !== undefined) {
            if (!data.content || data.content.trim().length === 0) {
                throw new Error('帖子内容不能为空');
            }
            if (data.content.length > 1000) {
                throw new Error('帖子内容不能超过1000字符');
            }
        }
        if (data.images !== undefined && data.images.length > 9) {
            throw new Error('最多只能上传9张图片');
        }
        // 构建更新数据
        const updateData = {
            updated_at: new Date()
        };
        if (data.title !== undefined) {
            updateData.title = data.title.trim();
        }
        if (data.content !== undefined) {
            updateData.content = data.content.trim();
        }
        if (data.images !== undefined) {
            updateData.images = data.images;
        }
        const updatedThread = await thread_repo_1.default.updateByThreadId(threadId, updateData);
        return updatedThread;
    }
    // 删除帖子
    async deleteThread(threadId, userId) {
        const thread = await thread_repo_1.default.findByIdWithUser(threadId);
        if (!thread || thread.user_id !== userId) {
            return false;
        }
        await thread_repo_1.default.updateStatus(threadId, 'deleted');
        // 如果是主帖，减少用户发帖数
        if (!thread.parent_thread_id) {
            await this.decrementUserThreadsCount(userId);
        }
        else {
            // 如果是回复，减少父帖子的回复数
            await thread_repo_1.default.decrementReplyCount(thread.parent_thread_id);
        }
        return true;
    }
    // 点赞或取消点赞
    async toggleLike(threadId, userId) {
        const thread = await thread_repo_1.default.findByThreadId(threadId);
        if (!thread || thread.status !== 'visible') {
            return null;
        }
        const isLiked = thread.liked_by.includes(userId);
        let updatedThread;
        if (isLiked) {
            updatedThread = await thread_repo_1.default.removeLike(threadId, userId);
            await this.decrementUserLikesGivenCount(userId);
        }
        else {
            updatedThread = await thread_repo_1.default.addLike(threadId, userId);
            await this.incrementUserLikesGivenCount(userId);
        }
        if (!updatedThread) {
            return null;
        }
        return {
            liked: !isLiked,
            like_count: updatedThread.like_count
        };
    }
    // 收藏或取消收藏
    async toggleBookmark(threadId, userId) {
        const thread = await thread_repo_1.default.findByThreadId(threadId);
        if (!thread || thread.status !== 'visible') {
            return null;
        }
        const isBookmarked = thread.bookmarked_by.includes(userId);
        let updatedThread;
        if (isBookmarked) {
            updatedThread = await thread_repo_1.default.removeBookmark(threadId, userId);
            await this.decrementUserBookmarksCount(userId);
        }
        else {
            updatedThread = await thread_repo_1.default.addBookmark(threadId, userId);
            await this.incrementUserBookmarksCount(userId);
        }
        if (!updatedThread) {
            return null;
        }
        return {
            bookmarked: !isBookmarked,
            bookmark_count: updatedThread.bookmark_count
        };
    }
    // 获取用户发布的帖子
    async getUserThreads(userId, page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        const threads = await thread_repo_1.default.findThreadsByUserId(userId, skip, limit);
        const total = await thread_repo_1.default.countThreadsByUserId(userId);
        return {
            threads,
            total,
            page,
            limit
        };
    }
    // 获取用户点赞的帖子
    async getUserLikedThreads(userId, page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        const threads = await thread_repo_1.default.findUserLikedThreads(userId, skip, limit);
        const total = await thread_repo_1.default.countUserLikedThreads(userId);
        return {
            threads,
            total,
            page,
            limit
        };
    }
    // 获取用户收藏的帖子
    async getUserBookmarkedThreads(userId, page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        const threads = await thread_repo_1.default.findUserBookmarkedThreads(userId, skip, limit);
        const total = await thread_repo_1.default.countUserBookmarkedThreads(userId);
        return {
            threads,
            total,
            page,
            limit
        };
    }
    // 获取用户的评论（回复）
    async getUserComments(userId, page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        const comments = await thread_repo_1.default.findUserComments(userId, skip, limit);
        const total = await thread_repo_1.default.countUserComments(userId);
        return {
            threads: comments,
            total,
            page,
            limit
        };
    }
    // 获取关注者的帖子动态
    async getFollowingThreads(userId, page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        const threads = await thread_repo_1.default.findFollowingThreads(userId, skip, limit);
        const total = await thread_repo_1.default.countFollowingThreads(userId);
        return {
            threads,
            total,
            page,
            limit
        };
    }
    // 辅助方法：增加用户发帖数
    async incrementUserThreadsCount(userId) {
        try {
            await font_user_repo_1.default.update(userId, { $inc: { threads_count: 1 } });
        }
        catch (error) {
            console.error('Failed to increment user threads count:', error);
        }
    }
    // 辅助方法：减少用户发帖数
    async decrementUserThreadsCount(userId) {
        try {
            await font_user_repo_1.default.update(userId, { $inc: { threads_count: -1 } });
        }
        catch (error) {
            console.error('Failed to decrement user threads count:', error);
        }
    }
    // 辅助方法：增加用户点赞数
    async incrementUserLikesGivenCount(userId) {
        try {
            await font_user_repo_1.default.update(userId, { $inc: { likes_given_count: 1 } });
        }
        catch (error) {
            console.error('Failed to increment user likes given count:', error);
        }
    }
    // 辅助方法：减少用户点赞数
    async decrementUserLikesGivenCount(userId) {
        try {
            await font_user_repo_1.default.update(userId, { $inc: { likes_given_count: -1 } });
        }
        catch (error) {
            console.error('Failed to decrement user likes given count:', error);
        }
    }
    // 辅助方法：增加用户收藏数
    async incrementUserBookmarksCount(userId) {
        try {
            await font_user_repo_1.default.update(userId, { $inc: { bookmarks_count: 1 } });
        }
        catch (error) {
            console.error('Failed to increment user bookmarks count:', error);
        }
    }
    // 辅助方法：减少用户收藏数
    async decrementUserBookmarksCount(userId) {
        try {
            await font_user_repo_1.default.update(userId, { $inc: { bookmarks_count: -1 } });
        }
        catch (error) {
            console.error('Failed to decrement user bookmarks count:', error);
        }
    }
}
const threadService = new ThreadService();
exports.default = threadService;
//# sourceMappingURL=thread-service.js.map