{"version": 3, "file": "thread-service.js", "sourceRoot": "", "sources": ["../../src/services/thread-service.ts"], "names": [], "mappings": ";;;;;AAAA,4EAAmD;AACnD,kFAAwD;AAExD,+BAAoC;AAEpC,MAAM,aAAa;IACjB,WAAW;IACX,KAAK,CAAC,YAAY,CAAC,MAAc,EAAE,IAAyB;QAC1D,MAAM,QAAQ,GAAG,IAAA,SAAM,GAAE,CAAC;QAE1B,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,cAAc,GAAuB,SAAS,CAAC;QAEnD,mBAAmB;QACnB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,MAAM,YAAY,GAAG,MAAM,qBAAU,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC5E,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC7C,CAAC;YAED,SAAS;YACT,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS;YAElE,UAAU;YACV,cAAc,GAAG,YAAY,CAAC,cAAc,IAAI,YAAY,CAAC,SAAS,CAAC;YAEvE,eAAe;YACf,IAAI,YAAY,CAAC,WAAW,IAAI,CAAC,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;QAED,MAAM,UAAU,GAAe;YAC7B,SAAS,EAAE,QAAQ;YACnB,OAAO,EAAE,MAAM;YACf,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,WAAW;YACvD,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YAC5B,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,EAAE;YACzB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,IAAI,SAAS;YACpD,cAAc,EAAE,cAAc;YAC9B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,IAAI,SAAS;YACpD,WAAW,EAAE,WAAW;YACxB,QAAQ,EAAE,EAAE;YACZ,aAAa,EAAE,EAAE;YACjB,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,CAAC;YACd,cAAc,EAAE,CAAC;YACjB,MAAM,EAAE,SAAS;YACjB,WAAW,EAAE,KAAK;YAClB,WAAW,EAAE,IAAI;YACjB,WAAW,EAAE,IAAI;YACjB,aAAa,EAAE,IAAI;YACnB,UAAU,EAAE,IAAI,IAAI,EAAE;YACtB,UAAU,EAAE,IAAI,IAAI,EAAE;SACvB,CAAC;QAEF,OAAO;QACP,MAAM,aAAa,GAAG,MAAM,qBAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAE1D,UAAU;QACV,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,eAAe;YACf,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;QAC/C,CAAC;aAAM,CAAC;YACN,0BAA0B;YAC1B,MAAM,qBAAU,CAAC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC5D,IAAI,cAAc,IAAI,cAAc,KAAK,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC/D,MAAM,qBAAU,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,WAAW;IACX,KAAK,CAAC,gBAAgB,CAAC,OAAe,CAAC,EAAE,QAAgB,EAAE,EAAE,SAAuC,QAAQ;QAC1G,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAChC,MAAM,OAAO,GAAG,MAAM,qBAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACxE,MAAM,KAAK,GAAG,MAAM,qBAAU,CAAC,kBAAkB,EAAE,CAAC;QAEpD,OAAO;YACL,OAAO;YACP,KAAK;YACL,IAAI;YACJ,KAAK;SACN,CAAC;IACJ,CAAC;IAED,YAAY;IACZ,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,eAAuB,CAAC,EAAE,gBAAwB,EAAE;QAC1F,MAAM,MAAM,GAAG,MAAM,qBAAU,CAAC,sBAAsB,CAAC,QAAQ,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;QAE9F,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;YACL,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;SAC1B,CAAC;IACJ,CAAC;IAED,SAAS;IACT,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,MAAc,EAAE,IAA6D;QAChH,MAAM,MAAM,GAAG,MAAM,qBAAU,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAE3D,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS;QACT,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAClD,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC;YAC9B,CAAC;YACD,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YAC/B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtD,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC;YAC9B,CAAC;YACD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;gBAC/B,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxD,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;QAChC,CAAC;QAED,SAAS;QACT,MAAM,UAAU,GAAQ;YACtB,UAAU,EAAE,IAAI,IAAI,EAAE;SACvB,CAAC;QAEF,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC7B,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QACvC,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YAC/B,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAC3C,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC9B,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAClC,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,qBAAU,CAAC,gBAAgB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAC9E,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,OAAO;IACP,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,MAAc;QACjD,MAAM,MAAM,GAAG,MAAM,qBAAU,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAE3D,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;YACzC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,qBAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAEnD,gBAAgB;QAChB,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAC7B,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;QAC/C,CAAC;aAAM,CAAC;YACN,kBAAkB;YAClB,MAAM,qBAAU,CAAC,mBAAmB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,UAAU;IACV,KAAK,CAAC,UAAU,CAAC,QAAgB,EAAE,MAAc;QAC/C,MAAM,MAAM,GAAG,MAAM,qBAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAEzD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACjD,IAAI,aAAgC,CAAC;QAErC,IAAI,OAAO,EAAE,CAAC;YACZ,aAAa,GAAG,MAAM,qBAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC9D,MAAM,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,MAAM,qBAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC3D,MAAM,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;YACL,KAAK,EAAE,CAAC,OAAO;YACf,UAAU,EAAE,aAAa,CAAC,UAAU;SACrC,CAAC;IACJ,CAAC;IAED,UAAU;IACV,KAAK,CAAC,cAAc,CAAC,QAAgB,EAAE,MAAc;QACnD,MAAM,MAAM,GAAG,MAAM,qBAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAEzD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC3D,IAAI,aAAgC,CAAC;QAErC,IAAI,YAAY,EAAE,CAAC;YACjB,aAAa,GAAG,MAAM,qBAAU,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAClE,MAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;QACjD,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,MAAM,qBAAU,CAAC,WAAW,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC/D,MAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;YACL,UAAU,EAAE,CAAC,YAAY;YACzB,cAAc,EAAE,aAAa,CAAC,cAAc;SAC7C,CAAC;IACJ,CAAC;IAED,YAAY;IACZ,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,OAAe,CAAC,EAAE,QAAgB,EAAE;QACvE,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAChC,MAAM,OAAO,GAAG,MAAM,qBAAU,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC1E,MAAM,KAAK,GAAG,MAAM,qBAAU,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAE5D,OAAO;YACL,OAAO;YACP,KAAK;YACL,IAAI;YACJ,KAAK;SACN,CAAC;IACJ,CAAC;IAED,YAAY;IACZ,KAAK,CAAC,mBAAmB,CAAC,MAAc,EAAE,OAAe,CAAC,EAAE,QAAgB,EAAE;QAC5E,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAChC,MAAM,OAAO,GAAG,MAAM,qBAAU,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC3E,MAAM,KAAK,GAAG,MAAM,qBAAU,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAE7D,OAAO;YACL,OAAO;YACP,KAAK;YACL,IAAI;YACJ,KAAK;SACN,CAAC;IACJ,CAAC;IAED,YAAY;IACZ,KAAK,CAAC,wBAAwB,CAAC,MAAc,EAAE,OAAe,CAAC,EAAE,QAAgB,EAAE;QACjF,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAChC,MAAM,OAAO,GAAG,MAAM,qBAAU,CAAC,yBAAyB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAChF,MAAM,KAAK,GAAG,MAAM,qBAAU,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;QAElE,OAAO;YACL,OAAO;YACP,KAAK;YACL,IAAI;YACJ,KAAK;SACN,CAAC;IACJ,CAAC;IAED,cAAc;IACd,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,OAAe,CAAC,EAAE,QAAgB,EAAE;QACxE,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAChC,MAAM,QAAQ,GAAG,MAAM,qBAAU,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACxE,MAAM,KAAK,GAAG,MAAM,qBAAU,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAEzD,OAAO;YACL,OAAO,EAAE,QAAQ;YACjB,KAAK;YACL,IAAI;YACJ,KAAK;SACN,CAAC;IACJ,CAAC;IAED,aAAa;IACb,KAAK,CAAC,mBAAmB,CAAC,MAAc,EAAE,OAAe,CAAC,EAAE,QAAgB,EAAE;QAC5E,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAChC,MAAM,OAAO,GAAG,MAAM,qBAAU,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC3E,MAAM,KAAK,GAAG,MAAM,qBAAU,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAE7D,OAAO;YACL,OAAO;YACP,KAAK;YACL,IAAI;YACJ,KAAK;SACN,CAAC;IACJ,CAAC;IAED,eAAe;IACP,KAAK,CAAC,yBAAyB,CAAC,MAAc;QACpD,IAAI,CAAC;YACH,MAAM,wBAAY,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE,EAAS,CAAC,CAAC;QAC3E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED,eAAe;IACP,KAAK,CAAC,yBAAyB,CAAC,MAAc;QACpD,IAAI,CAAC;YACH,MAAM,wBAAY,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC,EAAE,EAAS,CAAC,CAAC;QAC5E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED,eAAe;IACP,KAAK,CAAC,4BAA4B,CAAC,MAAc;QACvD,IAAI,CAAC;YACH,MAAM,wBAAY,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,EAAE,iBAAiB,EAAE,CAAC,EAAE,EAAS,CAAC,CAAC;QAC/E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED,eAAe;IACP,KAAK,CAAC,4BAA4B,CAAC,MAAc;QACvD,IAAI,CAAC;YACH,MAAM,wBAAY,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,EAAE,iBAAiB,EAAE,CAAC,CAAC,EAAE,EAAS,CAAC,CAAC;QAChF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED,eAAe;IACP,KAAK,CAAC,2BAA2B,CAAC,MAAc;QACtD,IAAI,CAAC;YACH,MAAM,wBAAY,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,EAAE,eAAe,EAAE,CAAC,EAAE,EAAS,CAAC,CAAC;QAC7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED,eAAe;IACP,KAAK,CAAC,2BAA2B,CAAC,MAAc;QACtD,IAAI,CAAC;YACH,MAAM,wBAAY,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,EAAE,eAAe,EAAE,CAAC,CAAC,EAAE,EAAS,CAAC,CAAC;QAC9E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;CACF;AAED,MAAM,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC;AAC1C,kBAAe,aAAa,CAAC"}