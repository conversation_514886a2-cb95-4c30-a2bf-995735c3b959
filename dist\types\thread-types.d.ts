export interface ThreadData {
    thread_id: string;
    user_id: string;
    title: string;
    content: string;
    images?: string[];
    parent_thread_id?: string;
    root_thread_id?: string;
    reply_to_user_id?: string;
    reply_level: number;
    liked_by: string[];
    bookmarked_by: string[];
    like_count: number;
    reply_count: number;
    bookmark_count: number;
    status: 'visible' | 'hidden' | 'deleted';
    is_approved: boolean;
    approved_by?: string | null;
    approved_at?: Date | null;
    reject_reason?: string | null;
    created_at: Date;
    updated_at: Date;
}
export interface ThreadWithUser extends ThreadData {
    user: {
        id: string;
        username: string;
        avatar?: string;
        bio?: string;
    };
    reply_to_user?: {
        id: string;
        username: string;
        avatar?: string;
    };
    replay_tread?: ThreadWithUser[];
}
export interface CreateThreadRequest {
    title?: string;
    content: string;
    images?: string[];
    parent_thread_id?: string;
    reply_to_user_id?: string;
}
export interface ThreadListResponse {
    threads: ThreadWithUser[];
    total: number;
    page: number;
    limit: number;
}
export interface ThreadDetailResponse {
    thread: ThreadWithUser;
    comments: {
        items: ThreadWithUser[];
        total: number;
        page: number;
        limit: number;
    };
}
//# sourceMappingURL=thread-types.d.ts.map