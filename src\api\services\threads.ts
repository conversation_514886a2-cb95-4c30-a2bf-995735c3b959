import apiClient from "../apiClient";

export interface Thread {
	_id: string;
	thread_id: string;
	user_id: string;
	title: string;
	content: string;
	images?: string[];
	parent_thread_id?: string;
	liked_by: string[];
	bookmarked_by: string[];
	like_count: number;
	reply_count: number;
	bookmark_count: number;
	status: "visible" | "hidden" | "deleted";
	is_approved?: boolean;
	approved_by?: string | null;
	approved_at?: string | null;
	reject_reason?: string | null;
	created_at: string;
	updated_at: string;
	user: {
		id: string;
		username: string;
		avatar?: string;
		bio?: string;
	};
}

export interface ThreadListResponse {
	threads: Thread[];
	total: number;
	page: number;
	limit: number;
}

export interface PendingThreadsResponse {
	items: Thread[];
	total: number;
	page: number;
	limit: number;
}

export interface ThreadDetailResponse {
	thread: Thread;
	comments: {
		items: Thread[];
		total: number;
		page: number;
		limit: number;
	};
}

export interface CreateThreadRequest {
	title?: string; // 对于回复来说是可选的
	content: string;
	images?: string[];
	parent_thread_id?: string;
}

export interface UpdateThreadRequest {
	title?: string;
	content?: string;
	images?: string[];
	status?: "visible" | "hidden" | "deleted";
}

export enum ThreadApi {
	Threads = "threads",
}

// Moderation APIs
export const getPendingThreads = async (page = 1, limit = 20) => {
	const response = await apiClient.get<PendingThreadsResponse>({
		url: `${ThreadApi.Threads}/moderation/pending`,
		params: { page, limit },
	});
	return response;
};

export const approveThread = async (threadId: string) => {
	const response = await apiClient.post<{ success: boolean; data: Thread }>({
		url: `${ThreadApi.Threads}/${threadId}/approve`,
	});
	return response;
};

export const rejectThread = async (threadId: string, reason?: string) => {
	const response = await apiClient.post<{ success: boolean; data: Thread }>({
		url: `${ThreadApi.Threads}/${threadId}/reject`,
		data: { reason },
	});
	return response;
};

// 批量审核通过
export const batchApproveThreads = async (threadIds: string[]) => {
	const response = await apiClient.post<{
		success: boolean;
		data: { modifiedCount: number; matchedCount: number };
		message: string;
	}>({
		url: `${ThreadApi.Threads}/batch/approve`,
		data: { threadIds },
	});
	return response;
};

// 批量审核拒绝
export const batchRejectThreads = async (threadIds: string[], reason?: string) => {
	const response = await apiClient.post<{
		success: boolean;
		data: { modifiedCount: number; matchedCount: number };
		message: string;
	}>({
		url: `${ThreadApi.Threads}/batch/reject`,
		data: { threadIds, reason },
	});
	return response;
};

// 批量删除帖子
export const batchDeleteThreads = async (threadIds: string[]) => {
	const response = await apiClient.post<{
		success: boolean;
		data: { modifiedCount: number; matchedCount: number };
		message: string;
	}>({
		url: `${ThreadApi.Threads}/batch/delete`,
		data: { threadIds },
	});
	return response;
};

// Get all threads with pagination and sorting
const getThreads = async (page = 1, limit = 10, sortBy = "latest") => {
	try {
		const response = await apiClient.get<ThreadListResponse>({
			url: `${ThreadApi.Threads}`,
			params: { page, limit, sort_by: sortBy },
		});
		return response;
	} catch (error) {
		console.error("Threads API error:", error);
		throw error;
	}
};

// Get thread by ID with comments
const getThreadById = async (threadId: string, commentsPage = 1, commentsLimit = 10) => {
	try {
		const response = await apiClient.get<ThreadDetailResponse>({
			url: `${ThreadApi.Threads}/${threadId}`,
			params: { comments_page: commentsPage, comments_limit: commentsLimit },
		});
		return response;
	} catch (error) {
		console.error(`Thread API error for ID ${threadId}:`, error);
		throw error;
	}
};

// Get threads by user ID
const getThreadsByUserId = async (userId: string, page = 1, limit = 10) => {
	try {
		const response = await apiClient.get<ThreadListResponse>({
			url: `${ThreadApi.Threads}/users/${userId}/threads`,
			params: { page, limit },
		});
		return response;
	} catch (error) {
		console.error(`User threads API error for user ${userId}:`, error);
		throw error;
	}
};

// Get user liked threads
const getUserLikedThreads = async (userId: string, page = 1, limit = 10) => {
	try {
		const response = await apiClient.get<ThreadListResponse>({
			url: `${ThreadApi.Threads}/users/${userId}/likes`,
			params: { page, limit },
		});
		return response;
	} catch (error) {
		console.error(`User liked threads API error for user ${userId}:`, error);
		throw error;
	}
};

// Get user bookmarked threads
const getUserBookmarkedThreads = async (userId: string, page = 1, limit = 10) => {
	try {
		const response = await apiClient.get<ThreadListResponse>({
			url: `${ThreadApi.Threads}/users/${userId}/bookmarks`,
			params: { page, limit },
			headers: { "user-id": userId }, // Required for access control
		});
		return response;
	} catch (error) {
		console.error(`User bookmarked threads API error for user ${userId}:`, error);
		throw error;
	}
};

// Create new thread
const createThread = async (threadData: CreateThreadRequest, userId: string) => {
	try {
		const response = await apiClient.post<Thread>({
			url: `${ThreadApi.Threads}`,
			data: threadData,
			headers: { "user-id": userId },
		});
		return response;
	} catch (error) {
		console.error("Thread creation API error:", error);
		throw error;
	}
};

// Create anonymous thread (admin only)
const createAnonymousThread = async (threadData: CreateThreadRequest) => {
	try {
		const response = await apiClient.post<Thread>({
			url: `${ThreadApi.Threads}/anonymous`,
			data: threadData,
		});
		return response;
	} catch (error) {
		console.error("Anonymous thread creation API error:", error);
		throw error;
	}
};

// Update thread (admin only - for content moderation)
const updateThread = async (threadId: string, threadData: UpdateThreadRequest, userId?: string) => {
	try {
		const headers: any = {};
		if (userId) {
			headers["user-id"] = userId;
		}

		const response = await apiClient.patch<Thread>({
			url: `${ThreadApi.Threads}/${threadId}`,
			data: threadData,
			headers,
		});
		return response;
	} catch (error) {
		console.error(`Thread update API error for ID ${threadId}:`, error);
		throw error;
	}
};

// Delete thread
const deleteThread = async (threadId: string, userId: string) => {
	try {
		const response = await apiClient.delete<{ success: boolean; message: string }>({
			url: `${ThreadApi.Threads}/${threadId}`,
			headers: { "user-id": userId },
		});
		return response;
	} catch (error) {
		console.error(`Thread delete API error for ID ${threadId}:`, error);
		throw error;
	}
};

// Toggle like on thread
const toggleLike = async (threadId: string, userId: string) => {
	try {
		const response = await apiClient.post<{ liked: boolean; like_count: number }>({
			url: `${ThreadApi.Threads}/${threadId}/like`,
			headers: { "user-id": userId },
		});
		return response;
	} catch (error) {
		console.error(`Thread like API error for ID ${threadId}:`, error);
		throw error;
	}
};

// Toggle bookmark on thread
const toggleBookmark = async (threadId: string, userId: string) => {
	try {
		const response = await apiClient.post<{ bookmarked: boolean; bookmark_count: number }>({
			url: `${ThreadApi.Threads}/${threadId}/bookmark`,
			headers: { "user-id": userId },
		});
		return response;
	} catch (error) {
		console.error(`Thread bookmark API error for ID ${threadId}:`, error);
		throw error;
	}
};

// Admin function: Update thread status (hide/show/delete)
const updateThreadStatus = async (threadId: string, status: "visible" | "hidden" | "deleted") => {
	try {
		const response = await apiClient.patch<Thread>({
			url: `${ThreadApi.Threads}/${threadId}/status`,
			data: { status },
		});
		return response;
	} catch (error) {
		console.error(`Thread status update API error for ID ${threadId}:`, error);
		throw error;
	}
};

export default {
	getThreads,
	getThreadById,
	getThreadsByUserId,
	getUserLikedThreads,
	getUserBookmarkedThreads,
	createThread,
	createAnonymousThread,
	updateThread,
	deleteThread,
	toggleLike,
	toggleBookmark,
	updateThreadStatus,
	getPendingThreads,
	approveThread,
	rejectThread,
	batchApproveThreads,
	batchRejectThreads,
	batchDeleteThreads,
};
