import type { Thread } from "@/api/services/threads";
import threadsService from "@/api/services/threads";
import { Icon } from "@/components/icon";
import { useThreadsAdmin } from "@/store/threadsStore";
import { Badge } from "@/ui/badge";
import { <PERSON><PERSON> } from "@/ui/button";
import { Card, CardContent, CardHeader } from "@/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/ui/select";
import { buildImageUrl } from "@/utils/imageUtils";
import { Image, Modal, Popconfirm, Space, message } from "antd";
import Table, { type ColumnsType } from "antd/es/table";
import { useState } from "react";
import { CreateThreadModal } from "./create-thread-modal";
import { ThreadDetailModal } from "./thread-detail-modal";
import { ThreadModal } from "./thread-modal";

export default function ThreadsPage() {
	const {
		data,
		pagination,
		isLoading,
		isFetching,
		isDeleting,
		isUpdatingStatus,
		currentPage,
		sortBy,
		statusFilter,
		setPage,
		setSortBy,
		setStatusFilter,
		updateThreadStatus,
		deleteThread,
		refetch,
	} = useThreadsAdmin();

	const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
	const [batchLoading, setBatchLoading] = useState(false);

	const [threadModalProps, setThreadModalProps] = useState<{
		thread: Thread | null;
		show: boolean;
		onCancel: () => void;
	}>({
		thread: null,
		show: false,
		onCancel: () => {
			setThreadModalProps((prev) => ({ ...prev, show: false }));
		},
	});

	const [detailModalProps, setDetailModalProps] = useState<{
		threadId: string | null;
		show: boolean;
		onCancel: () => void;
	}>({
		threadId: null,
		show: false,
		onCancel: () => {
			setDetailModalProps((prev) => ({ ...prev, show: false }));
		},
	});

	const [createModalProps, setCreateModalProps] = useState<{
		show: boolean;
		onCancel: () => void;
	}>({
		show: false,
		onCancel: () => {
			setCreateModalProps((prev) => ({ ...prev, show: false }));
		},
	});

	// 批量删除帖子
	const handleBatchDelete = () => {
		if (selectedRowKeys.length === 0) {
			message.warning("请选择要删除的帖子");
			return;
		}

		Modal.confirm({
			title: `确定删除 ${selectedRowKeys.length} 个帖子？`,
			content: "删除后的帖子将无法恢复，请谨慎操作。",
			okText: "确定删除",
			okType: "danger",
			cancelText: "取消",
			onOk: async () => {
				try {
					setBatchLoading(true);
					const result = await threadsService.batchDeleteThreads(selectedRowKeys);
					message.success(result.message || `成功删除 ${selectedRowKeys.length} 个帖子`);
					setSelectedRowKeys([]);
					refetch?.();
				} catch (e: any) {
					message.error(e?.message || "批量删除失败");
				} finally {
					setBatchLoading(false);
				}
			},
		});
	};

	const getStatusBadge = (status: string) => {
		const statusMap = {
			visible: { label: "显示", variant: "default" as const, color: "green" },
			hidden: { label: "隐藏", variant: "secondary" as const, color: "orange" },
			deleted: { label: "删除", variant: "destructive" as const, color: "red" },
		};

		const statusInfo = statusMap[status as keyof typeof statusMap];

		return statusInfo ? (
			<Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>
		) : (
			<Badge variant="outline">{status}</Badge>
		);
	};

	const columns: ColumnsType<Thread> = [
		{
			title: "标题",
			dataIndex: "title",
			width: 200,
			render: (title: string, record) => (
				<div
					className="cursor-pointer hover:text-blue-600"
					onClick={() => {
						setDetailModalProps({
							threadId: record.thread_id,
							show: true,
							onCancel: () => setDetailModalProps((prev) => ({ ...prev, show: false })),
						});
					}}
				>
					{title ? (
						<span className="font-medium text-gray-900 line-clamp-2">{title}</span>
					) : (
						<span className="text-gray-400 italic">无标题</span>
					)}
				</div>
			),
		},
		{
			title: "内容",
			dataIndex: "content",
			width: 300,
			render: (content: string, record) => (
				<div className="space-y-2">
					<div
						className="text-sm text-gray-600 line-clamp-3 cursor-pointer hover:text-blue-600"
						onClick={() => {
							setDetailModalProps({
								threadId: record.thread_id,
								show: true,
								onCancel: () => setDetailModalProps((prev) => ({ ...prev, show: false })),
							});
						}}
					>
						{content}
					</div>
					{record.images && record.images.length > 0 && (
						<div className="flex gap-1">
							<Image.PreviewGroup>
								{record.images.slice(0, 3).map((img, index) => (
									<Image
										key={`${record.thread_id}-img-${index}`}
										src={buildImageUrl(img)}
										alt={`image-${index}`}
										width={40}
										height={40}
										className="rounded object-cover"
									/>
								))}
							</Image.PreviewGroup>
							{record.images.length > 3 && (
								<div className="w-10 h-10 bg-gray-100 rounded flex items-center justify-center text-xs">
									+{record.images.length - 3}
								</div>
							)}
						</div>
					)}
				</div>
			),
		},
		{
			title: "作者",
			key: "user",
			width: 150,
			render: (_, record) => (
				<div className="flex items-center space-x-2">
					{record.user.avatar ? (
						<img src={record.user.avatar} alt={record.user.username} className="w-8 h-8 rounded-full object-cover" />
					) : (
						<div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
							<Icon icon="lucide:user" className="w-4 h-4 text-gray-500" />
						</div>
					)}
					<div>
						<div className="font-medium text-sm">{record.user.username}</div>
						<div className="text-xs text-gray-500">{record.user.id}</div>
					</div>
				</div>
			),
		},
		{
			title: "类型",
			key: "type",
			width: 80,
			render: (_, record) => (
				<Badge variant={record.parent_thread_id ? "secondary" : "default"}>
					{record.parent_thread_id ? "回复" : "帖子"}
				</Badge>
			),
		},
		{
			title: "状态",
			dataIndex: "status",
			width: 80,
			render: (_: any, record) => (
				<div className="flex items-center gap-2">
					{getStatusBadge(record.status)}
					{record.is_approved === false && <Badge variant="secondary">待审核</Badge>}
				</div>
			),
		},
		{
			title: "互动数据",
			key: "stats",
			width: 120,
			render: (_, record) => (
				<div className="text-xs space-y-1">
					<div className="flex items-center gap-1">
						<Icon icon="lucide:heart" className="w-3 h-3 text-red-500" />
						{record.like_count}
					</div>
					<div className="flex items-center gap-1">
						<Icon icon="lucide:message-circle" className="w-3 h-3 text-blue-500" />
						{record.reply_count}
					</div>
					<div className="flex items-center gap-1">
						<Icon icon="lucide:bookmark" className="w-3 h-3 text-yellow-500" />
						{record.bookmark_count}
					</div>
				</div>
			),
		},
		{
			title: "发布时间",
			dataIndex: "created_at",
			width: 120,
			render: (createdAt: string) => <div className="text-sm">{new Date(createdAt).toLocaleString("zh-CN")}</div>,
		},
		{
			title: "操作",
			key: "operation",
			align: "center",
			width: 200,
			render: (_, record) => (
				<div className="flex justify-center">
					<Space.Compact>
						{record.is_approved === false && (
							<>
								<Popconfirm
									title="审核通过"
									description="确定审核通过此帖子吗？"
									onConfirm={() => updateThreadStatus({ threadId: record.thread_id, status: "visible" })}
									okText="确定"
									cancelText="取消"
								>
									<Button size="sm" variant="default">
										<Icon icon="lucide:check" size={14} />
									</Button>
								</Popconfirm>
								<Popconfirm
									title="驳回帖子"
									description="确定驳回此帖子吗？"
									onConfirm={() => updateThreadStatus({ threadId: record.thread_id, status: "hidden" })}
									okText="确定"
									cancelText="取消"
								>
									<Button size="sm" variant="secondary">
										<Icon icon="lucide:x" size={14} />
									</Button>
								</Popconfirm>
							</>
						)}
						<Button
							size="sm"
							onClick={() => {
								setDetailModalProps({
									threadId: record.thread_id,
									show: true,
									onCancel: () => setDetailModalProps((prev) => ({ ...prev, show: false })),
								});
							}}
						>
							<Icon icon="lucide:eye" size={14} />
						</Button>

						{record.status !== "hidden" && (
							<Popconfirm
								title="隐藏帖子"
								description="确定要隐藏这个帖子吗？"
								onConfirm={() => updateThreadStatus({ threadId: record.thread_id, status: "hidden" })}
								okText="确定"
								cancelText="取消"
							>
								<Button size="sm" variant="secondary">
									<Icon icon="lucide:eye-off" size={14} />
								</Button>
							</Popconfirm>
						)}

						{record.status === "hidden" && (
							<Popconfirm
								title="显示帖子"
								description="确定要显示这个帖子吗？"
								onConfirm={() => updateThreadStatus({ threadId: record.thread_id, status: "visible" })}
								okText="确定"
								cancelText="取消"
							>
								<Button size="sm" variant="default">
									<Icon icon="lucide:eye" size={14} />
								</Button>
							</Popconfirm>
						)}

						<Popconfirm
							title="删除帖子"
							description="确定要删除这个帖子吗？此操作不可恢复！"
							onConfirm={() => deleteThread({ threadId: record.thread_id, userId: record.user_id })}
							okText="确定"
							cancelText="取消"
						>
							<Button size="sm" variant="destructive">
								<Icon icon="lucide:trash-2" size={14} />
							</Button>
						</Popconfirm>
					</Space.Compact>
				</div>
			),
		},
	];

	return (
		<Card>
			<CardHeader className="pb-4">
				<div className="flex justify-between items-center">
					<div>
						<h2 className="text-xl font-semibold">广场帖子管理</h2>
						<p className="text-sm text-gray-600 mt-1">管理用户发布的帖子和回复内容</p>
					</div>

					<Button
						onClick={() => setCreateModalProps((prev) => ({ ...prev, show: true }))}
						className="flex items-center gap-2"
					>
						<Icon icon="lucide:plus" size={16} />
						管理员发帖
					</Button>
				</div>

				<div className="flex items-center gap-4 mt-4">
					<div className="flex items-center gap-2">
						<span className="text-sm text-gray-600">排序：</span>
						<Select value={sortBy} onValueChange={(value) => setSortBy(value as "latest" | "hot")}>
							<SelectTrigger className="w-32">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="latest">最新</SelectItem>
								<SelectItem value="hot">热门</SelectItem>
							</SelectContent>
						</Select>
					</div>

					<div className="flex items-center gap-2">
						<span className="text-sm text-gray-600">状态：</span>
						<Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as any)}>
							<SelectTrigger className="w-32">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">全部</SelectItem>
								<SelectItem value="visible">显示</SelectItem>
								<SelectItem value="hidden">隐藏</SelectItem>
								<SelectItem value="deleted">删除</SelectItem>
							</SelectContent>
						</Select>
					</div>

					<div className="flex-1"></div>

					<div className="text-sm text-gray-500">{pagination && `共 ${pagination.total} 条记录`}</div>
				</div>
			</CardHeader>

			<CardContent>
				{/* 批量操作按钮 */}
				{selectedRowKeys.length > 0 && (
					<div className="mb-4 p-3 bg-red-50 rounded-lg border border-red-200">
						<div className="flex items-center justify-between">
							<span className="text-sm text-red-700">已选择 {selectedRowKeys.length} 个帖子</span>
							<Button size="sm" variant="destructive" onClick={handleBatchDelete} disabled={batchLoading}>
								<Icon icon="lucide:trash-2" size={14} />
								批量删除
							</Button>
						</div>
					</div>
				)}

				<Table
					rowKey="thread_id"
					size="small"
					scroll={{ x: "max-content" }}
					pagination={{
						current: currentPage,
						total: pagination?.total || 0,
						pageSize: pagination?.limit || 20,
						showSizeChanger: false,
						showQuickJumper: true,
						showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
						onChange: setPage,
					}}
					columns={columns}
					dataSource={data}
					loading={isLoading || isFetching || isDeleting || isUpdatingStatus || batchLoading}
					rowSelection={{
						selectedRowKeys,
						onChange: (selectedRowKeys) => setSelectedRowKeys(selectedRowKeys as string[]),
						getCheckboxProps: (record) => ({
							name: record.thread_id,
							disabled: record.status === "deleted", // 已删除的帖子不能再选择
						}),
					}}
					rowClassName={(record) => {
						if (record.status === "deleted") return "opacity-50";
						if (record.status === "hidden") return "bg-gray-50";
						return "";
					}}
				/>
			</CardContent>

			<ThreadModal {...threadModalProps} />
			<ThreadDetailModal {...detailModalProps} />
			<CreateThreadModal {...createModalProps} />
		</Card>
	);
}
