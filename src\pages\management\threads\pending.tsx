import threadsService from "@/api/services/threads";
import { Icon } from "@/components/icon";
import { Badge } from "@/ui/badge";
import { Button } from "@/ui/button";
import { Card, CardContent, CardHeader } from "@/ui/card";
import { buildImageUrl } from "@/utils/imageUtils";
import { Dropdown, Input, Modal, Popconfirm, Space, Table, message } from "antd";
import type { ColumnsType } from "antd/es/table";
import { useState } from "react";

type Thread = import("@/api/services/threads").Thread;

export default function PendingThreadsPage() {
	const [page, setPage] = useState(1);
	const [limit] = useState(20);
	const [loading, setLoading] = useState(false);
	const [data, setData] = useState<Thread[]>([]);
	const [total, setTotal] = useState(0);
	const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
	const [batchLoading, setBatchLoading] = useState(false);

	const [hasInitialized, setHasInitialized] = useState(false);

	const fetchData = async (p = page) => {
		try {
			setLoading(true);
			const res = await threadsService.getPendingThreads(p, limit);
			setData(res.items || []);
			setTotal(res.total || 0);
			setPage(res.page || p);
			setHasInitialized(true);
		} catch (e: any) {
			message.error(e?.message || "获取待审核帖子失败");
			setHasInitialized(true);
		} finally {
			setLoading(false);
		}
	};

	// 初次加载 - 只执行一次
	if (!hasInitialized && !loading) {
		fetchData(1);
	}

	const handleApprove = async (threadId: string) => {
		try {
			await threadsService.approveThread(threadId);
			message.success("审核通过");
			fetchData();
		} catch (e: any) {
			message.error(e?.message || "审核失败");
		}
	};

	const promptReject = (threadId: string) => {
		let reasonValue = "";
		Modal.confirm({
			title: "驳回帖子",
			content: (
				<div className="mt-2">
					<div className="mb-2 text-sm text-gray-500">请输入驳回原因（可选）</div>
					<Input.TextArea
						rows={3}
						onChange={(e) => {
							reasonValue = e.target.value;
						}}
					/>
				</div>
			),
			okText: "确定驳回",
			cancelText: "取消",
			onOk: async () => {
				try {
					const r = (reasonValue || "").trim();
					await threadsService.rejectThread(threadId, r || undefined);
					message.success("已驳回");
					fetchData();
				} catch (e: any) {
					message.error(e?.message || "驳回失败");
				}
			},
		});
	};

	// 批量审核通过
	const handleBatchApprove = async () => {
		if (selectedRowKeys.length === 0) {
			message.warning("请选择要审核的帖子");
			return;
		}

		try {
			setBatchLoading(true);
			let successCount = 0;
			let failCount = 0;

			// 逐个审核通过
			for (const threadId of selectedRowKeys) {
				try {
					await threadsService.approveThread(threadId);
					successCount++;
				} catch (e) {
					failCount++;
					console.error(`审核帖子 ${threadId} 失败:`, e);
				}
			}

			if (successCount > 0) {
				message.success(`成功审核通过 ${successCount} 个帖子${failCount > 0 ? `，${failCount} 个失败` : ""}`);
			} else {
				message.error("批量审核失败");
			}

			setSelectedRowKeys([]);
			fetchData();
		} catch (e: any) {
			message.error(e?.message || "批量审核失败");
		} finally {
			setBatchLoading(false);
		}
	};

	// 批量删除
	const handleBatchDelete = () => {
		if (selectedRowKeys.length === 0) {
			message.warning("请选择要删除的帖子");
			return;
		}

		Modal.confirm({
			title: `确定删除 ${selectedRowKeys.length} 个帖子？`,
			content: "删除后的帖子将无法恢复，请谨慎操作。",
			okText: "确定删除",
			okType: "danger",
			cancelText: "取消",
			onOk: async () => {
				try {
					setBatchLoading(true);
					let successCount = 0;
					let failCount = 0;

					// 逐个删除帖子 - 使用管理员权限
					for (const threadId of selectedRowKeys) {
						try {
							// 使用 updateThreadStatus 将状态设为 deleted
							await threadsService.updateThreadStatus(threadId, "deleted");
							successCount++;
						} catch (e) {
							failCount++;
							console.error(`删除帖子 ${threadId} 失败:`, e);
						}
					}

					if (successCount > 0) {
						message.success(`成功删除 ${successCount} 个帖子${failCount > 0 ? `，${failCount} 个失败` : ""}`);
					} else {
						message.error("批量删除失败");
					}

					setSelectedRowKeys([]);
					fetchData();
				} catch (e: any) {
					message.error(e?.message || "批量删除失败");
				} finally {
					setBatchLoading(false);
				}
			},
		});
	};

	const columns: ColumnsType<Thread> = [
		{
			title: "标题",
			dataIndex: "title",
			width: 220,
			render: (title: string) =>
				title ? (
					<span className="font-medium line-clamp-2">{title}</span>
				) : (
					<span className="text-gray-400">无标题</span>
				),
		},
		{
			title: "内容",
			dataIndex: "content",
			width: 360,
			render: (content: string, record) => (
				<div className="space-y-2">
					<div className="text-sm text-gray-700 line-clamp-3">{content}</div>
					{record.images && record.images.length > 0 && (
						<div className="flex gap-1">
							{record.images.slice(0, 3).map((img, i) => (
								<img
									key={`${record.thread_id}-img-${i}`}
									src={buildImageUrl(img)}
									alt="img"
									className="w-10 h-10 rounded object-cover"
								/>
							))}
							{record.images.length > 3 && (
								<div className="w-10 h-10 bg-gray-100 rounded flex items-center justify-center text-xs">
									+{record.images.length - 3}
								</div>
							)}
						</div>
					)}
				</div>
			),
		},
		{
			title: "作者",
			key: "user",
			width: 160,
			render: (_, record) => (
				<div className="text-sm">
					<div className="font-medium">{record.user?.username || record.user_id}</div>
					<div className="text-gray-500 text-xs">{record.user?.id || record.user_id}</div>
				</div>
			),
		},
		{
			title: "类型",
			key: "type",
			width: 80,
			render: (_, r) => (
				<Badge variant={r.parent_thread_id ? "secondary" : "default"}>{r.parent_thread_id ? "回复" : "帖子"}</Badge>
			),
		},
		{
			title: "时间",
			dataIndex: "created_at",
			width: 160,
			render: (t: string) => new Date(t).toLocaleString("zh-CN"),
		},
		{
			title: "操作",
			key: "op",
			align: "center",
			width: 160,
			render: (_, r) => (
				<div className="flex justify-center gap-2">
					<Popconfirm
						title="审核通过此帖子？"
						okText="通过"
						cancelText="取消"
						onConfirm={() => handleApprove(r.thread_id)}
					>
						<Button size="sm">
							<Icon icon="lucide:check" size={16} />
						</Button>
					</Popconfirm>
					<Button size="sm" variant="secondary" onClick={() => promptReject(r.thread_id)}>
						<Icon icon="lucide:x" size={16} />
					</Button>
				</div>
			),
		},
	];

	return (
		<Card>
			<CardHeader>
				<div className="flex items-center justify-between">
					<div>
						<h2 className="text-xl font-semibold">待审核帖子</h2>
						<p className="text-sm text-gray-600 mt-1">审核通过后帖子将出现在广场</p>
					</div>
					<Button onClick={() => fetchData(1)} className="flex items-center gap-2">
						<Icon icon="lucide:refresh-ccw" size={16} /> 刷新
					</Button>
				</div>
			</CardHeader>
			<CardContent>
				{/* 批量操作按钮 */}
				{selectedRowKeys.length > 0 && (
					<div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
						<div className="flex items-center justify-between">
							<span className="text-sm text-blue-700">已选择 {selectedRowKeys.length} 个帖子</span>
							<Space>
								<Button
									size="sm"
									onClick={handleBatchApprove}
									disabled={batchLoading}
									className="bg-green-500 hover:bg-green-600 text-white disabled:opacity-50"
								>
									<Icon icon="lucide:check" size={14} />
									批量通过
								</Button>
								<Button size="sm" variant="destructive" onClick={handleBatchDelete} disabled={batchLoading}>
									<Icon icon="lucide:trash-2" size={14} />
									批量删除
								</Button>
							</Space>
						</div>
					</div>
				)}

				<Table
					rowKey="thread_id"
					columns={columns}
					dataSource={data}
					loading={loading || batchLoading}
					rowSelection={{
						selectedRowKeys,
						onChange: (selectedRowKeys) => setSelectedRowKeys(selectedRowKeys as string[]),
						getCheckboxProps: (record) => ({
							name: record.thread_id,
						}),
					}}
					pagination={{
						current: page,
						total,
						pageSize: limit,
						showSizeChanger: false,
						onChange: (p) => fetchData(p),
					}}
				/>
			</CardContent>
		</Card>
	);
}
