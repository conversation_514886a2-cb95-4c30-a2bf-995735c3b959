import express, { Request, Response, NextFunction } from 'express';
import threadService from '../services/thread-service';
import { CreateThreadRequest } from '../types/thread-types';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { ResponseData } from '../utils/response-data';

const router = express.Router();

// Configure multer for thread image uploads
const storage = multer.diskStorage({
    destination: (_req, _file, cb) => {
        const uploadDir = path.join(process.cwd(), 'uploads/threads');
        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        // Generate unique filename with timestamp
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname);
        const filename = `thread-${uniqueSuffix}${ext}`;
        cb(null, filename);
    }
});

const upload = multer({
    storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
    },
    fileFilter: (_req, file, cb) => {
        const allowedTypes = /jpeg|jpg|png|gif|webp/;
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);

        if (mimetype && extname) {
            return cb(null, true);
        } else {
            cb(new Error('只允许上传图片文件 (jpeg, jpg, png, gif, webp)'));
        }
    }
});

// 中间件：用户认证检查（这里假设存在，需要根据现有认证系统调整）
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    username: string;
  };
}

// 简单的认证中间件示例（需要根据实际情况调整）
const requireAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  // 这里应该实现实际的JWT token验证逻辑
  // 暂时使用header中的user-id作为简单认证
  const userId = req.headers['user-id'] as string;
  
  if (!userId) {
    res.status(401).json({
      success: false,
      message: '需要用户认证'
    });
    return;
  }

  req.user = { id: userId, username: 'user' };
  next();
};

// 可选认证中间件
const optionalAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  const userId = req.headers['user-id'] as string;
  
  if (userId) {
    req.user = { id: userId, username: 'user' };
  }
  
  next();
};

// POST /api/threads - 创建新帖子或回复
router.post('/', requireAuth, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { title, content, images, parent_thread_id }: CreateThreadRequest = req.body;

    // 验证必填字段
    // 如果是主帖（没有parent_thread_id），标题是必需的
    if (!parent_thread_id && (!title || title.trim().length === 0)) {
      res.status(400).json({
        success: false,
        message: '帖子标题不能为空'
      });
      return;
    }

    if (!content || content.trim().length === 0) {
      res.status(400).json({
        success: false,
        message: '帖子内容不能为空'
      });
      return;
    }

    // 如果提供了标题，验证长度
    if (title && title.length > 100) {
      res.status(400).json({
        success: false,
        message: '帖子标题不能超过100字符'
      });
      return;
    }

    if (content.length > 1000) {
      res.status(400).json({
        success: false,
        message: '帖子内容不能超过1000字符'
      });
      return;
    }

    if (images && images.length > 9) {
      res.status(400).json({
        success: false,
        message: '最多只能上传9张图片'
      });
      return;
    }

    const thread = await threadService.createThread(req.user!.id, {
      title,
      content,
      images,
      parent_thread_id
    });

    res.status(201).json({
      success: true,
      data: thread
    });
  } catch (error: any) {
    console.error('Create thread error:', error);
    res.status(500).json({
      success: false,
      message: '创建帖子失败',
      error: error.message
    });
  }
});

// GET /api/threads - 获取广场帖子列表
router.get('/', optionalAuth, async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 10, 50); // 限制最大50条
    const sortBy = (req.query.sort_by as string) || 'latest';

    if (page < 1) {
      res.status(400).json({
        success: false,
        message: '页码必须大于0'
      });
      return;
    }

    if (!['latest', 'updated', 'hot', 'following'].includes(sortBy)) {
      res.status(400).json({
        success: false,
        message: '排序方式只能是 latest、updated、hot 或 following'
      });
      return;
    }

    let result;

    if (sortBy === 'following') {
      // 关注者动态需要用户认证
      const userId = req.headers['user-id'] as string;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: '获取关注动态需要用户认证'
        });
        return;
      }
      result = await threadService.getFollowingThreads(userId, page, limit);
    } else {
      result = await threadService.getPublicThreads(page, limit, sortBy as 'latest' | 'updated' | 'hot');
    }

    res.json({
      success: true,
      data: result
    });
  } catch (error: any) {
    console.error('Get threads error:', error);
    res.status(500).json({
      success: false,
      message: '获取帖子列表失败',
      error: error.message
    });
  }
});

// 审核列表：待审核帖子（主帖与回复均可）
router.get('/moderation/pending', async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const skip = (page - 1) * limit;

    const Thread = (await import('../models/thread')).default;
    const items = await Thread.find({ is_approved: false, status: { $ne: 'deleted' } })
      .sort({ created_at: -1 })
      .skip(skip)
      .limit(limit);
    const total = await Thread.countDocuments({ is_approved: false, status: { $ne: 'deleted' } });

    res.json({ success: true, data: { items, total, page, limit } });
  } catch (error: any) {
    console.error('Get pending threads error:', error);
    res.status(500).json({ success: false, message: '获取待审核帖子失败', error: error.message });
  }
});

// 审核通过
router.post('/:id/approve', async (req: Request, res: Response): Promise<void> => {
  try {
    const threadId = req.params.id;
    const approver = (req.headers['x-admin-id'] as string) || 'admin';
    const Thread = (await import('../models/thread')).default;
    const updated = await Thread.findOneAndUpdate(
      { thread_id: threadId },
      { is_approved: true, approved_by: approver, approved_at: new Date(), reject_reason: null },
      { new: true },
    );
    if (!updated) {
      res.status(404).json({ success: false, message: '帖子不存在' });
      return;
    }
    res.json({ success: true, data: updated, message: '审核通过' });
  } catch (error: any) {
    console.error('Approve thread error:', error);
    res.status(500).json({ success: false, message: '审核失败', error: error.message });
  }
});

// 审核拒绝
router.post('/:id/reject', async (req: Request, res: Response): Promise<void> => {
  try {
    const threadId = req.params.id;
    const { reason } = req.body as { reason?: string };
    const approver = (req.headers['x-admin-id'] as string) || 'admin';
    const Thread = (await import('../models/thread')).default;
    const updated = await Thread.findOneAndUpdate(
      { thread_id: threadId },
      { is_approved: false, approved_by: approver, approved_at: null, reject_reason: reason || '不符合发布规范' },
      { new: true },
    );
    if (!updated) {
      res.status(404).json({ success: false, message: '帖子不存在' });
      return;
    }
    res.json({ success: true, data: updated, message: '已拒绝' });
  } catch (error: any) {
    console.error('Reject thread error:', error);
    res.status(500).json({ success: false, message: '审核失败', error: error.message });
  }
});

// 批量审核通过
router.post('/batch/approve', async (req: Request, res: Response): Promise<void> => {
  try {
    const { threadIds } = req.body as { threadIds: string[] };
    const approver = (req.headers['x-admin-id'] as string) || 'admin';

    if (!threadIds || !Array.isArray(threadIds) || threadIds.length === 0) {
      res.status(400).json({ success: false, message: '请提供有效的帖子ID列表' });
      return;
    }

    const Thread = (await import('../models/thread')).default;
    const result = await Thread.updateMany(
      { thread_id: { $in: threadIds } },
      {
        is_approved: true,
        approved_by: approver,
        approved_at: new Date(),
        reject_reason: null
      }
    );

    res.json({
      success: true,
      data: {
        modifiedCount: result.modifiedCount,
        matchedCount: result.matchedCount
      },
      message: `成功审核通过 ${result.modifiedCount} 个帖子`
    });
  } catch (error: any) {
    console.error('Batch approve threads error:', error);
    res.status(500).json({ success: false, message: '批量审核失败', error: error.message });
  }
});

// 批量审核拒绝
router.post('/batch/reject', async (req: Request, res: Response): Promise<void> => {
  try {
    const { threadIds, reason } = req.body as { threadIds: string[]; reason?: string };
    const approver = (req.headers['x-admin-id'] as string) || 'admin';

    if (!threadIds || !Array.isArray(threadIds) || threadIds.length === 0) {
      res.status(400).json({ success: false, message: '请提供有效的帖子ID列表' });
      return;
    }

    const Thread = (await import('../models/thread')).default;
    const result = await Thread.updateMany(
      { thread_id: { $in: threadIds } },
      {
        is_approved: false,
        approved_by: approver,
        approved_at: null,
        reject_reason: reason || '不符合发布规范'
      }
    );

    res.json({
      success: true,
      data: {
        modifiedCount: result.modifiedCount,
        matchedCount: result.matchedCount
      },
      message: `成功拒绝 ${result.modifiedCount} 个帖子`
    });
  } catch (error: any) {
    console.error('Batch reject threads error:', error);
    res.status(500).json({ success: false, message: '批量拒绝失败', error: error.message });
  }
});

// 批量删除帖子
router.post('/batch/delete', async (req: Request, res: Response): Promise<void> => {
  try {
    const { threadIds } = req.body as { threadIds: string[] };

    if (!threadIds || !Array.isArray(threadIds) || threadIds.length === 0) {
      res.status(400).json({ success: false, message: '请提供有效的帖子ID列表' });
      return;
    }

    const Thread = (await import('../models/thread')).default;
    const result = await Thread.updateMany(
      { thread_id: { $in: threadIds } },
      { status: 'deleted' }
    );

    res.json({
      success: true,
      data: {
        modifiedCount: result.modifiedCount,
        matchedCount: result.matchedCount
      },
      message: `成功删除 ${result.modifiedCount} 个帖子`
    });
  } catch (error: any) {
    console.error('Batch delete threads error:', error);
    res.status(500).json({ success: false, message: '批量删除失败', error: error.message });
  }
});

// GET /api/threads/:id - 获取单个帖子详情和评论
router.get('/:id', optionalAuth, async (req: Request, res: Response): Promise<void> => {
  try {
    const threadId = req.params.id;
    const commentsPage = parseInt(req.query.comments_page as string) || 1;
    const commentsLimit = Math.min(parseInt(req.query.comments_limit as string) || 10, 50);

    if (commentsPage < 1) {
      res.status(400).json({
        success: false,
        message: '评论页码必须大于0'
      });
      return;
    }

    const result = await threadService.getThreadDetail(threadId, commentsPage, commentsLimit);

    if (!result) {
      res.status(404).json({
        success: false,
        message: '帖子不存在'
      });
      return;
    }

    res.json({
      success: true,
      data: result
    });
  } catch (error: any) {
    console.error('Get thread detail error:', error);
    res.status(500).json({
      success: false,
      message: '获取帖子详情失败',
      error: error.message
    });
  }
});

// PATCH /api/threads/:id - 更新帖子内容
router.patch('/:id', requireAuth, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const threadId = req.params.id;
    const { title, content, images } = req.body;

    const updatedThread = await threadService.updateThread(threadId, req.user!.id, {
      title,
      content,
      images
    });

    if (!updatedThread) {
      res.status(404).json({
        success: false,
        message: '帖子不存在或无权限编辑'
      });
      return;
    }

    res.json({
      success: true,
      data: updatedThread,
      message: '帖子更新成功'
    });
  } catch (error: any) {
    console.error('Update thread error:', error);
    res.status(500).json({
      success: false,
      message: error.message || '更新帖子失败',
      error: error.message
    });
  }
});

// DELETE /api/threads/:id - 删除帖子
router.delete('/:id', requireAuth, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const threadId = req.params.id;
    const success = await threadService.deleteThread(threadId, req.user!.id);

    if (!success) {
      res.status(404).json({
        success: false,
        message: '帖子不存在或无权限删除'
      });
      return;
    }

    res.json({
      success: true,
      message: '帖子已删除'
    });
  } catch (error: any) {
    console.error('Delete thread error:', error);
    res.status(500).json({
      success: false,
      message: '删除帖子失败',
      error: error.message
    });
  }
});

// POST /api/threads/:id/like - 点赞或取消点赞
router.post('/:id/like', requireAuth, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const threadId = req.params.id;
    const result = await threadService.toggleLike(threadId, req.user!.id);

    if (!result) {
      res.status(404).json({
        success: false,
        message: '帖子不存在'
      });
      return;
    }

    res.json({
      success: true,
      data: result
    });
  } catch (error: any) {
    console.error('Toggle like error:', error);
    res.status(500).json({
      success: false,
      message: '点赞操作失败',
      error: error.message
    });
  }
});

// POST /api/threads/:id/bookmark - 收藏或取消收藏
router.post('/:id/bookmark', requireAuth, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const threadId = req.params.id;
    const result = await threadService.toggleBookmark(threadId, req.user!.id);

    if (!result) {
      res.status(404).json({
        success: false,
        message: '帖子不存在'
      });
      return;
    }

    res.json({
      success: true,
      data: result
    });
  } catch (error: any) {
    console.error('Toggle bookmark error:', error);
    res.status(500).json({
      success: false,
      message: '收藏操作失败',
      error: error.message
    });
  }
});

// GET /api/users/:userId/threads - 获取指定用户的帖子
router.get('/users/:userId/threads', optionalAuth, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.params.userId;
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);

    if (page < 1) {
      res.status(400).json({
        success: false,
        message: '页码必须大于0'
      });
      return;
    }

    const result = await threadService.getUserThreads(userId, page, limit);

    res.json({
      success: true,
      data: result
    });
  } catch (error: any) {
    console.error('Get user threads error:', error);
    res.status(500).json({
      success: false,
      message: '获取用户帖子失败',
      error: error.message
    });
  }
});

// GET /api/users/:userId/likes - 获取指定用户点赞的帖子
router.get('/users/:userId/likes', optionalAuth, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.params.userId;
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);

    if (page < 1) {
      res.status(400).json({
        success: false,
        message: '页码必须大于0'
      });
      return;
    }

    const result = await threadService.getUserLikedThreads(userId, page, limit);

    res.json({
      success: true,
      data: result
    });
  } catch (error: any) {
    console.error('Get user liked threads error:', error);
    res.status(500).json({
      success: false,
      message: '获取用户点赞帖子失败',
      error: error.message
    });
  }
});

// GET /api/users/:userId/bookmarks - 获取指定用户收藏的帖子
router.get('/users/:userId/bookmarks', requireAuth, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.params.userId;
    
    // 只允许用户查看自己的收藏
    if (userId !== req.user!.id) {
      res.status(403).json({
        success: false,
        message: '只能查看自己的收藏'
      });
      return;
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);

    if (page < 1) {
      res.status(400).json({
        success: false,
        message: '页码必须大于0'
      });
      return;
    }

    const result = await threadService.getUserBookmarkedThreads(userId, page, limit);

    res.json({
      success: true,
      data: result
    });
  } catch (error: any) {
    console.error('Get user bookmarked threads error:', error);
    res.status(500).json({
      success: false,
      message: '获取用户收藏帖子失败',
      error: error.message
    });
  }
});

// GET /api/threads/users/:userId/comments - 获取指定用户的评论（回复）
router.get('/users/:userId/comments', optionalAuth, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.params.userId;
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);

    if (page < 1) {
      res.status(400).json({
        success: false,
        message: '页码必须大于0'
      });
      return;
    }

    const result = await threadService.getUserComments(userId, page, limit);

    res.json({
      success: true,
      data: result
    });
  } catch (error: any) {
    console.error('Get user comments error:', error);
    res.status(500).json({
      success: false,
      message: '获取用户评论失败',
      error: error.message
    });
  }
});

// POST /api/threads/anonymous - 管理员匿名发帖
router.post('/anonymous', async (req: Request, res: Response): Promise<void> => {
  try {
    const { title, content, images }: CreateThreadRequest = req.body;
    
    console.log('Anonymous thread creation request:', { title, content, images }); // 调试日志

    // 验证必填字段
    if (!title || title.trim().length === 0) {
      res.status(400).json({
        success: false,
        message: '帖子标题不能为空'
      });
      return;
    }

    if (!content || content.trim().length === 0) {
      res.status(400).json({
        success: false,
        message: '帖子内容不能为空'
      });
      return;
    }

    if (title.length > 100) {
      res.status(400).json({
        success: false,
        message: '帖子标题不能超过100字符'
      });
      return;
    }

    if (content.length > 1000) {
      res.status(400).json({
        success: false,
        message: '帖子内容不能超过1000字符'
      });
      return;
    }

    if (images && images.length > 9) {
      res.status(400).json({
        success: false,
        message: '最多只能上传9张图片'
      });
      return;
    }

    // 使用匿名用户ID发帖
    const anonymousUserId = 'anonymous-user';
    const thread = await threadService.createThread(anonymousUserId, {
      title,
      content,
      images,
      parent_thread_id: undefined // 匿名发帖不能是回复
    });

    res.status(201).json({
      success: true,
      data: thread
    });
  } catch (error: any) {
    console.error('Create anonymous thread error:', error);
    res.status(500).json({
      success: false,
      message: '创建帖子失败',
      error: error.message
    });
  }
});

// POST /api/threads/upload-image - 上传帖子图片
router.post('/upload-image', upload.single('image'), ((req: Request & { file?: any }, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json(ResponseData.error("没有上传文件"));
    }

    // 返回相对路径而不是完整URL，让前端通过环境变量拼接
    const relativePath = `uploads/threads/${req.file.filename}`;

    res.json(ResponseData.success({
      imageUrl: relativePath,
      filename: req.file.filename,
    }));
  } catch (error: any) {
    console.error('Thread image upload error:', error);
    res.status(500).json(ResponseData.error(error.message || "图片上传失败"));
  }
}) as express.RequestHandler);

export default router;
